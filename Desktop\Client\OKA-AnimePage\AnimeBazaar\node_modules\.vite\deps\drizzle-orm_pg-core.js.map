{"version": 3, "sources": ["../../src/pg-core/alias.ts", "../../src/pg-core/checks.ts", "../../src/selection-proxy.ts", "../../src/pg-core/query-builders/delete.ts", "../../src/casing.ts", "../../src/pg-core/view-base.ts", "../../src/pg-core/dialect.ts", "../../src/query-builders/query-builder.ts", "../../src/pg-core/query-builders/select.ts", "../../src/pg-core/query-builders/query-builder.ts", "../../src/pg-core/query-builders/insert.ts", "../../src/pg-core/query-builders/refresh-materialized-view.ts", "../../src/pg-core/query-builders/update.ts", "../../src/pg-core/query-builders/count.ts", "../../src/pg-core/query-builders/query.ts", "../../src/pg-core/query-builders/raw.ts", "../../src/pg-core/db.ts", "../../src/pg-core/indexes.ts", "../../src/pg-core/policies.ts", "../../src/pg-core/roles.ts", "../../src/pg-core/sequence.ts", "../../src/pg-core/view-common.ts", "../../src/pg-core/view.ts", "../../src/pg-core/schema.ts", "../../src/pg-core/session.ts", "../../src/pg-core/utils.ts"], "sourcesContent": ["import { TableAliasProxyHandler } from '~/alias.ts';\nimport type { BuildAliasTable } from './query-builders/select.types.ts';\n\nimport type { PgTable } from './table.ts';\nimport type { PgViewBase } from './view-base.ts';\n\nexport function alias<TTable extends PgTable | PgViewBase, TAlias extends string>(\n\ttable: TTable,\n\talias: T<PERSON><PERSON><PERSON>,\n): BuildAliasTable<TTable, TAlias> {\n\treturn new Proxy(table, new TableAliasProxyHandler(alias, false)) as any;\n}\n", "import { entityKind } from '~/entity.ts';\nimport type { SQL } from '~/sql/index.ts';\nimport type { PgTable } from './table.ts';\n\nexport class CheckBuilder {\n\tstatic readonly [entityKind]: string = 'PgCheckBuilder';\n\n\tprotected brand!: 'PgConstraintBuilder';\n\n\tconstructor(public name: string, public value: SQL) {}\n\n\t/** @internal */\n\tbuild(table: PgTable): Check {\n\t\treturn new Check(table, this);\n\t}\n}\n\nexport class Check {\n\tstatic readonly [entityKind]: string = 'PgCheck';\n\n\treadonly name: string;\n\treadonly value: SQL;\n\n\tconstructor(public table: PgTable, builder: CheckBuilder) {\n\t\tthis.name = builder.name;\n\t\tthis.value = builder.value;\n\t}\n}\n\nexport function check(name: string, value: SQL): CheckBuilder {\n\treturn new CheckBuilder(name, value);\n}\n", "import { ColumnAliasProxyHandler, TableAliasProxyHandler } from './alias.ts';\nimport { Column } from './column.ts';\nimport { entityKind, is } from './entity.ts';\nimport { SQL, View } from './sql/sql.ts';\nimport { Subquery } from './subquery.ts';\nimport { ViewBaseConfig } from './view-common.ts';\n\nexport class SelectionProxyHandler<T extends Subquery | Record<string, unknown> | View>\n\timplements ProxyHandler<Subquery | Record<string, unknown> | View>\n{\n\tstatic readonly [entityKind]: string = 'SelectionProxyHandler';\n\n\tprivate config: {\n\t\t/**\n\t\t * Table alias for the columns\n\t\t */\n\t\talias?: string;\n\t\t/**\n\t\t * What to do when a field is an instance of `SQL.Aliased` and it's not a selection field (from a subquery)\n\t\t *\n\t\t * `sql` - return the underlying SQL expression\n\t\t *\n\t\t * `alias` - return the field alias\n\t\t */\n\t\tsqlAliasedBehavior: 'sql' | 'alias';\n\t\t/**\n\t\t * What to do when a field is an instance of `SQL` and it doesn't have an alias declared\n\t\t *\n\t\t * `sql` - return the underlying SQL expression\n\t\t *\n\t\t * `error` - return a DrizzleTypeError on type level and throw an error on runtime\n\t\t */\n\t\tsqlBehavior: 'sql' | 'error';\n\n\t\t/**\n\t\t * Whether to replace the original name of the column with the alias\n\t\t * Should be set to `true` for views creation\n\t\t * @default false\n\t\t */\n\t\treplaceOriginalName?: boolean;\n\t};\n\n\tconstructor(config: SelectionProxyHandler<T>['config']) {\n\t\tthis.config = { ...config };\n\t}\n\n\tget(subquery: T, prop: string | symbol): any {\n\t\tif (prop === '_') {\n\t\t\treturn {\n\t\t\t\t...subquery['_' as keyof typeof subquery],\n\t\t\t\tselectedFields: new Proxy(\n\t\t\t\t\t(subquery as Subquery)._.selectedFields,\n\t\t\t\t\tthis as ProxyHandler<Record<string, unknown>>,\n\t\t\t\t),\n\t\t\t};\n\t\t}\n\n\t\tif (prop === ViewBaseConfig) {\n\t\t\treturn {\n\t\t\t\t...subquery[ViewBaseConfig as keyof typeof subquery],\n\t\t\t\tselectedFields: new Proxy(\n\t\t\t\t\t(subquery as View)[ViewBaseConfig].selectedFields,\n\t\t\t\t\tthis as ProxyHandler<Record<string, unknown>>,\n\t\t\t\t),\n\t\t\t};\n\t\t}\n\n\t\tif (typeof prop === 'symbol') {\n\t\t\treturn subquery[prop as keyof typeof subquery];\n\t\t}\n\n\t\tconst columns = is(subquery, Subquery)\n\t\t\t? subquery._.selectedFields\n\t\t\t: is(subquery, View)\n\t\t\t? subquery[ViewBaseConfig].selectedFields\n\t\t\t: subquery;\n\t\tconst value: unknown = columns[prop as keyof typeof columns];\n\n\t\tif (is(value, SQL.Aliased)) {\n\t\t\t// Never return the underlying SQL expression for a field previously selected in a subquery\n\t\t\tif (this.config.sqlAliasedBehavior === 'sql' && !value.isSelectionField) {\n\t\t\t\treturn value.sql;\n\t\t\t}\n\n\t\t\tconst newValue = value.clone();\n\t\t\tnewValue.isSelectionField = true;\n\t\t\treturn newValue;\n\t\t}\n\n\t\tif (is(value, SQL)) {\n\t\t\tif (this.config.sqlBehavior === 'sql') {\n\t\t\t\treturn value;\n\t\t\t}\n\n\t\t\tthrow new Error(\n\t\t\t\t`You tried to reference \"${prop}\" field from a subquery, which is a raw SQL field, but it doesn't have an alias declared. Please add an alias to the field using \".as('alias')\" method.`,\n\t\t\t);\n\t\t}\n\n\t\tif (is(value, Column)) {\n\t\t\tif (this.config.alias) {\n\t\t\t\treturn new Proxy(\n\t\t\t\t\tvalue,\n\t\t\t\t\tnew ColumnAliasProxyHandler(\n\t\t\t\t\t\tnew Proxy(\n\t\t\t\t\t\t\tvalue.table,\n\t\t\t\t\t\t\tnew TableAliasProxyHandler(this.config.alias, this.config.replaceOriginalName ?? false),\n\t\t\t\t\t\t),\n\t\t\t\t\t),\n\t\t\t\t);\n\t\t\t}\n\t\t\treturn value;\n\t\t}\n\n\t\tif (typeof value !== 'object' || value === null) {\n\t\t\treturn value;\n\t\t}\n\n\t\treturn new Proxy(value, new SelectionProxyHandler(this.config));\n\t}\n}\n", "import { entityKind } from '~/entity.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport type {\n\tPgPreparedQuery,\n\tPgQueryResultHKT,\n\tPgQueryResultKind,\n\tPgSession,\n\tPreparedQueryConfig,\n} from '~/pg-core/session.ts';\nimport type { PgTable } from '~/pg-core/table.ts';\nimport { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport type { SelectResultFields } from '~/query-builders/select.types.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport { SelectionProxyHandler } from '~/selection-proxy.ts';\nimport type { ColumnsSelection, Query, SQL, SQLWrapper } from '~/sql/sql.ts';\nimport type { Subquery } from '~/subquery.ts';\nimport { getTableName, Table } from '~/table.ts';\nimport { tracer } from '~/tracing.ts';\nimport { type NeonAuthToken, orderSelectedFields } from '~/utils.ts';\nimport type { PgColumn } from '../columns/common.ts';\nimport type { SelectedFieldsFlat, SelectedFieldsOrdered } from './select.types.ts';\n\nexport type PgDeleteWithout<\n\tT extends AnyPgDeleteBase,\n\tTDynamic extends boolean,\n\tK extends keyof T & string,\n> = TDynamic extends true ? T\n\t: Omit<\n\t\tPgDeleteBase<\n\t\t\tT['_']['table'],\n\t\t\tT['_']['queryResult'],\n\t\t\tT['_']['selectedFields'],\n\t\t\tT['_']['returning'],\n\t\t\tTDynamic,\n\t\t\tT['_']['excludedMethods'] | K\n\t\t>,\n\t\tT['_']['excludedMethods'] | K\n\t>;\n\nexport type PgDelete<\n\tTTable extends PgTable = PgTable,\n\tTQueryResult extends PgQueryResultHKT = PgQueryResultHKT,\n\tTSelectedFields extends ColumnsSelection | undefined = undefined,\n\tTReturning extends Record<string, unknown> | undefined = Record<string, unknown> | undefined,\n> = PgDeleteBase<TTable, TQueryResult, TSelectedFields, TReturning, true, never>;\n\nexport interface PgDeleteConfig {\n\twhere?: SQL | undefined;\n\ttable: PgTable;\n\treturningFields?: SelectedFieldsFlat;\n\treturning?: SelectedFieldsOrdered;\n\twithList?: Subquery[];\n}\n\nexport type PgDeleteReturningAll<\n\tT extends AnyPgDeleteBase,\n\tTDynamic extends boolean,\n> = PgDeleteWithout<\n\tPgDeleteBase<\n\t\tT['_']['table'],\n\t\tT['_']['queryResult'],\n\t\tT['_']['table']['_']['columns'],\n\t\tT['_']['table']['$inferSelect'],\n\t\tTDynamic,\n\t\tT['_']['excludedMethods']\n\t>,\n\tTDynamic,\n\t'returning'\n>;\n\nexport type PgDeleteReturning<\n\tT extends AnyPgDeleteBase,\n\tTDynamic extends boolean,\n\tTSelectedFields extends SelectedFieldsFlat,\n> = PgDeleteWithout<\n\tPgDeleteBase<\n\t\tT['_']['table'],\n\t\tT['_']['queryResult'],\n\t\tTSelectedFields,\n\t\tSelectResultFields<TSelectedFields>,\n\t\tTDynamic,\n\t\tT['_']['excludedMethods']\n\t>,\n\tTDynamic,\n\t'returning'\n>;\n\nexport type PgDeletePrepare<T extends AnyPgDeleteBase> = PgPreparedQuery<\n\tPreparedQueryConfig & {\n\t\texecute: T['_']['returning'] extends undefined ? PgQueryResultKind<T['_']['queryResult'], never>\n\t\t\t: T['_']['returning'][];\n\t}\n>;\n\nexport type PgDeleteDynamic<T extends AnyPgDeleteBase> = PgDelete<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tT['_']['selectedFields'],\n\tT['_']['returning']\n>;\n\nexport type AnyPgDeleteBase = PgDeleteBase<any, any, any, any, any, any>;\n\nexport interface PgDeleteBase<\n\tTTable extends PgTable,\n\tTQueryResult extends PgQueryResultHKT,\n\tTSelectedFields extends ColumnsSelection | undefined = undefined,\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n> extends\n\tTypedQueryBuilder<\n\t\tTSelectedFields,\n\t\tTReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[]\n\t>,\n\tQueryPromise<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[]>,\n\tRunnableQuery<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[], 'pg'>,\n\tSQLWrapper\n{\n\treadonly _: {\n\t\treadonly dialect: 'pg';\n\t\treadonly table: TTable;\n\t\treadonly queryResult: TQueryResult;\n\t\treadonly selectedFields: TSelectedFields;\n\t\treadonly returning: TReturning;\n\t\treadonly dynamic: TDynamic;\n\t\treadonly excludedMethods: TExcludedMethods;\n\t\treadonly result: TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[];\n\t};\n}\n\nexport class PgDeleteBase<\n\tTTable extends PgTable,\n\tTQueryResult extends PgQueryResultHKT,\n\tTSelectedFields extends ColumnsSelection | undefined = undefined,\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n\tTDynamic extends boolean = false,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTExcludedMethods extends string = never,\n> extends QueryPromise<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[]>\n\timplements\n\t\tTypedQueryBuilder<\n\t\t\tTSelectedFields,\n\t\t\tTReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[]\n\t\t>,\n\t\tRunnableQuery<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[], 'pg'>,\n\t\tSQLWrapper\n{\n\tstatic override readonly [entityKind]: string = 'PgDelete';\n\n\tprivate config: PgDeleteConfig;\n\n\tconstructor(\n\t\ttable: TTable,\n\t\tprivate session: PgSession,\n\t\tprivate dialect: PgDialect,\n\t\twithList?: Subquery[],\n\t) {\n\t\tsuper();\n\t\tthis.config = { table, withList };\n\t}\n\n\t/**\n\t * Adds a `where` clause to the query.\n\t *\n\t * Calling this method will delete only those rows that fulfill a specified condition.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/delete}\n\t *\n\t * @param where the `where` clause.\n\t *\n\t * @example\n\t * You can use conditional operators and `sql function` to filter the rows to be deleted.\n\t *\n\t * ```ts\n\t * // Delete all cars with green color\n\t * await db.delete(cars).where(eq(cars.color, 'green'));\n\t * // or\n\t * await db.delete(cars).where(sql`${cars.color} = 'green'`)\n\t * ```\n\t *\n\t * You can logically combine conditional operators with `and()` and `or()` operators:\n\t *\n\t * ```ts\n\t * // Delete all BMW cars with a green color\n\t * await db.delete(cars).where(and(eq(cars.color, 'green'), eq(cars.brand, 'BMW')));\n\t *\n\t * // Delete all cars with the green or blue color\n\t * await db.delete(cars).where(or(eq(cars.color, 'green'), eq(cars.color, 'blue')));\n\t * ```\n\t */\n\twhere(where: SQL | undefined): PgDeleteWithout<this, TDynamic, 'where'> {\n\t\tthis.config.where = where;\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds a `returning` clause to the query.\n\t *\n\t * Calling this method will return the specified fields of the deleted rows. If no fields are specified, all fields will be returned.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/delete#delete-with-return}\n\t *\n\t * @example\n\t * ```ts\n\t * // Delete all cars with the green color and return all fields\n\t * const deletedCars: Car[] = await db.delete(cars)\n\t *   .where(eq(cars.color, 'green'))\n\t *   .returning();\n\t *\n\t * // Delete all cars with the green color and return only their id and brand fields\n\t * const deletedCarsIdsAndBrands: { id: number, brand: string }[] = await db.delete(cars)\n\t *   .where(eq(cars.color, 'green'))\n\t *   .returning({ id: cars.id, brand: cars.brand });\n\t * ```\n\t */\n\treturning(): PgDeleteReturningAll<this, TDynamic>;\n\treturning<TSelectedFields extends SelectedFieldsFlat>(\n\t\tfields: TSelectedFields,\n\t): PgDeleteReturning<this, TDynamic, TSelectedFields>;\n\treturning(\n\t\tfields: SelectedFieldsFlat = this.config.table[Table.Symbol.Columns],\n\t): PgDeleteReturning<this, TDynamic, any> {\n\t\tthis.config.returningFields = fields;\n\t\tthis.config.returning = orderSelectedFields<PgColumn>(fields);\n\t\treturn this as any;\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this.dialect.buildDeleteQuery(this.config);\n\t}\n\n\ttoSQL(): Query {\n\t\tconst { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n\t\treturn rest;\n\t}\n\n\t/** @internal */\n\t_prepare(name?: string): PgDeletePrepare<this> {\n\t\treturn tracer.startActiveSpan('drizzle.prepareQuery', () => {\n\t\t\treturn this.session.prepareQuery<\n\t\t\t\tPreparedQueryConfig & {\n\t\t\t\t\texecute: TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[];\n\t\t\t\t}\n\t\t\t>(this.dialect.sqlToQuery(this.getSQL()), this.config.returning, name, true);\n\t\t});\n\t}\n\n\tprepare(name: string): PgDeletePrepare<this> {\n\t\treturn this._prepare(name);\n\t}\n\n\tprivate authToken?: NeonAuthToken;\n\t/** @internal */\n\tsetToken(token?: NeonAuthToken) {\n\t\tthis.authToken = token;\n\t\treturn this;\n\t}\n\n\toverride execute: ReturnType<this['prepare']>['execute'] = (placeholderValues) => {\n\t\treturn tracer.startActiveSpan('drizzle.operation', () => {\n\t\t\treturn this._prepare().execute(placeholderValues, this.authToken);\n\t\t});\n\t};\n\n\t/** @internal */\n\tgetSelectedFields(): this['_']['selectedFields'] {\n\t\treturn (\n\t\t\tthis.config.returningFields\n\t\t\t\t? new Proxy(\n\t\t\t\t\tthis.config.returningFields,\n\t\t\t\t\tnew SelectionProxyHandler({\n\t\t\t\t\t\talias: getTableName(this.config.table),\n\t\t\t\t\t\tsqlAliasedBehavior: 'alias',\n\t\t\t\t\t\tsqlBehavior: 'error',\n\t\t\t\t\t}),\n\t\t\t\t)\n\t\t\t\t: undefined\n\t\t) as this['_']['selectedFields'];\n\t}\n\n\t$dynamic(): PgDeleteDynamic<this> {\n\t\treturn this as any;\n\t}\n}\n", "import type { Column } from '~/column.ts';\nimport { entityKind } from './entity.ts';\nimport { Table } from './table.ts';\nimport type { Casing } from './utils.ts';\n\nexport function toSnakeCase(input: string) {\n\tconst words = input\n\t\t.replace(/['\\u2019]/g, '')\n\t\t.match(/[\\da-z]+|[A-Z]+(?![a-z])|[A-Z][\\da-z]+/g) ?? [];\n\n\treturn words.map((word) => word.toLowerCase()).join('_');\n}\n\nexport function toCamelCase(input: string) {\n\tconst words = input\n\t\t.replace(/['\\u2019]/g, '')\n\t\t.match(/[\\da-z]+|[A-Z]+(?![a-z])|[A-Z][\\da-z]+/g) ?? [];\n\n\treturn words.reduce((acc, word, i) => {\n\t\tconst formattedWord = i === 0 ? word.toLowerCase() : `${word[0]!.toUpperCase()}${word.slice(1)}`;\n\t\treturn acc + formattedWord;\n\t}, '');\n}\n\nfunction noopCase(input: string) {\n\treturn input;\n}\n\nexport class CasingCache {\n\tstatic readonly [entityKind]: string = 'CasingCache';\n\n\t/** @internal */\n\tcache: Record<string, string> = {};\n\tprivate cachedTables: Record<string, true> = {};\n\tprivate convert: (input: string) => string;\n\n\tconstructor(casing?: Casing) {\n\t\tthis.convert = casing === 'snake_case'\n\t\t\t? toSnakeCase\n\t\t\t: casing === 'camelCase'\n\t\t\t? toCamelCase\n\t\t\t: noopCase;\n\t}\n\n\tgetColumnCasing(column: Column): string {\n\t\tif (!column.keyAsName) return column.name;\n\n\t\tconst schema = column.table[Table.Symbol.Schema] ?? 'public';\n\t\tconst tableName = column.table[Table.Symbol.OriginalName];\n\t\tconst key = `${schema}.${tableName}.${column.name}`;\n\n\t\tif (!this.cache[key]) {\n\t\t\tthis.cacheTable(column.table);\n\t\t}\n\t\treturn this.cache[key]!;\n\t}\n\n\tprivate cacheTable(table: Table) {\n\t\tconst schema = table[Table.Symbol.Schema] ?? 'public';\n\t\tconst tableName = table[Table.Symbol.OriginalName];\n\t\tconst tableKey = `${schema}.${tableName}`;\n\n\t\tif (!this.cachedTables[tableKey]) {\n\t\t\tfor (const column of Object.values(table[Table.Symbol.Columns])) {\n\t\t\t\tconst columnKey = `${tableKey}.${column.name}`;\n\t\t\t\tthis.cache[columnKey] = this.convert(column.name);\n\t\t\t}\n\t\t\tthis.cachedTables[tableKey] = true;\n\t\t}\n\t}\n\n\tclearCache() {\n\t\tthis.cache = {};\n\t\tthis.cachedTables = {};\n\t}\n}\n", "import { entityKind } from '~/entity.ts';\nimport { type ColumnsSelection, View } from '~/sql/sql.ts';\n\nexport abstract class PgViewBase<\n\tTName extends string = string,\n\tTExisting extends boolean = boolean,\n\tTSelectedFields extends ColumnsSelection = ColumnsSelection,\n> extends View<TName, TExisting, TSelectedFields> {\n\tstatic override readonly [entityKind]: string = 'PgViewBase';\n\n\tdeclare readonly _: View<TName, TExisting, TSelectedFields>['_'] & {\n\t\treadonly viewBrand: 'PgViewBase';\n\t};\n}\n", "import { aliasedTable, aliasedTableColumn, mapColumnsInAliasedSQLToAlias, mapColumnsInSQLToAlias } from '~/alias.ts';\nimport { CasingCache } from '~/casing.ts';\nimport { Column } from '~/column.ts';\nimport { entityKind, is } from '~/entity.ts';\nimport { DrizzleError } from '~/errors.ts';\nimport type { MigrationConfig, MigrationMeta } from '~/migrator.ts';\nimport {\n\tPgColumn,\n\tPgDate,\n\tPgDateString,\n\tPgJson,\n\tPgJsonb,\n\tPgNumeric,\n\tPgTime,\n\tPgTimestamp,\n\tPgTimestampString,\n\tPgUUID,\n} from '~/pg-core/columns/index.ts';\nimport type {\n\tAnyPgSelectQueryBuilder,\n\tPgDeleteConfig,\n\tPgInsertConfig,\n\tPgSelectJoinConfig,\n\tPgUpdateConfig,\n} from '~/pg-core/query-builders/index.ts';\nimport type { PgSelectConfig, SelectedFieldsOrdered } from '~/pg-core/query-builders/select.types.ts';\nimport { PgTable } from '~/pg-core/table.ts';\nimport {\n\ttype BuildRelationalQueryResult,\n\ttype DBQueryConfig,\n\tgetOperators,\n\tgetOrderByOperators,\n\tMany,\n\tnormalizeRelation,\n\tOne,\n\ttype Relation,\n\ttype TableRelationalConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport { and, eq, View } from '~/sql/index.ts';\nimport {\n\ttype DriverValueEncoder,\n\ttype Name,\n\tParam,\n\ttype QueryTypingsValue,\n\ttype QueryWithTypings,\n\tSQL,\n\tsql,\n\ttype SQLChunk,\n} from '~/sql/sql.ts';\nimport { Subquery } from '~/subquery.ts';\nimport { getTableName, getTableUniqueName, Table } from '~/table.ts';\nimport { type Casing, orderSelectedFields, type UpdateSet } from '~/utils.ts';\nimport { ViewBaseConfig } from '~/view-common.ts';\nimport type { PgSession } from './session.ts';\nimport { PgViewBase } from './view-base.ts';\nimport type { PgMaterializedView } from './view.ts';\n\nexport interface PgDialectConfig {\n\tcasing?: Casing;\n}\n\nexport class PgDialect {\n\tstatic readonly [entityKind]: string = 'PgDialect';\n\n\t/** @internal */\n\treadonly casing: CasingCache;\n\n\tconstructor(config?: PgDialectConfig) {\n\t\tthis.casing = new CasingCache(config?.casing);\n\t}\n\n\tasync migrate(migrations: MigrationMeta[], session: PgSession, config: string | MigrationConfig): Promise<void> {\n\t\tconst migrationsTable = typeof config === 'string'\n\t\t\t? '__drizzle_migrations'\n\t\t\t: config.migrationsTable ?? '__drizzle_migrations';\n\t\tconst migrationsSchema = typeof config === 'string' ? 'drizzle' : config.migrationsSchema ?? 'drizzle';\n\t\tconst migrationTableCreate = sql`\n\t\t\tCREATE TABLE IF NOT EXISTS ${sql.identifier(migrationsSchema)}.${sql.identifier(migrationsTable)} (\n\t\t\t\tid SERIAL PRIMARY KEY,\n\t\t\t\thash text NOT NULL,\n\t\t\t\tcreated_at bigint\n\t\t\t)\n\t\t`;\n\t\tawait session.execute(sql`CREATE SCHEMA IF NOT EXISTS ${sql.identifier(migrationsSchema)}`);\n\t\tawait session.execute(migrationTableCreate);\n\n\t\tconst dbMigrations = await session.all<{ id: number; hash: string; created_at: string }>(\n\t\t\tsql`select id, hash, created_at from ${sql.identifier(migrationsSchema)}.${\n\t\t\t\tsql.identifier(migrationsTable)\n\t\t\t} order by created_at desc limit 1`,\n\t\t);\n\n\t\tconst lastDbMigration = dbMigrations[0];\n\t\tawait session.transaction(async (tx) => {\n\t\t\tfor await (const migration of migrations) {\n\t\t\t\tif (\n\t\t\t\t\t!lastDbMigration\n\t\t\t\t\t|| Number(lastDbMigration.created_at) < migration.folderMillis\n\t\t\t\t) {\n\t\t\t\t\tfor (const stmt of migration.sql) {\n\t\t\t\t\t\tawait tx.execute(sql.raw(stmt));\n\t\t\t\t\t}\n\t\t\t\t\tawait tx.execute(\n\t\t\t\t\t\tsql`insert into ${sql.identifier(migrationsSchema)}.${\n\t\t\t\t\t\t\tsql.identifier(migrationsTable)\n\t\t\t\t\t\t} (\"hash\", \"created_at\") values(${migration.hash}, ${migration.folderMillis})`,\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t}\n\n\tescapeName(name: string): string {\n\t\treturn `\"${name}\"`;\n\t}\n\n\tescapeParam(num: number): string {\n\t\treturn `$${num + 1}`;\n\t}\n\n\tescapeString(str: string): string {\n\t\treturn `'${str.replace(/'/g, \"''\")}'`;\n\t}\n\n\tprivate buildWithCTE(queries: Subquery[] | undefined): SQL | undefined {\n\t\tif (!queries?.length) return undefined;\n\n\t\tconst withSqlChunks = [sql`with `];\n\t\tfor (const [i, w] of queries.entries()) {\n\t\t\twithSqlChunks.push(sql`${sql.identifier(w._.alias)} as (${w._.sql})`);\n\t\t\tif (i < queries.length - 1) {\n\t\t\t\twithSqlChunks.push(sql`, `);\n\t\t\t}\n\t\t}\n\t\twithSqlChunks.push(sql` `);\n\t\treturn sql.join(withSqlChunks);\n\t}\n\n\tbuildDeleteQuery({ table, where, returning, withList }: PgDeleteConfig): SQL {\n\t\tconst withSql = this.buildWithCTE(withList);\n\n\t\tconst returningSql = returning\n\t\t\t? sql` returning ${this.buildSelection(returning, { isSingleTable: true })}`\n\t\t\t: undefined;\n\n\t\tconst whereSql = where ? sql` where ${where}` : undefined;\n\n\t\treturn sql`${withSql}delete from ${table}${whereSql}${returningSql}`;\n\t}\n\n\tbuildUpdateSet(table: PgTable, set: UpdateSet): SQL {\n\t\tconst tableColumns = table[Table.Symbol.Columns];\n\n\t\tconst columnNames = Object.keys(tableColumns).filter((colName) =>\n\t\t\tset[colName] !== undefined || tableColumns[colName]?.onUpdateFn !== undefined\n\t\t);\n\n\t\tconst setSize = columnNames.length;\n\t\treturn sql.join(columnNames.flatMap((colName, i) => {\n\t\t\tconst col = tableColumns[colName]!;\n\n\t\t\tconst value = set[colName] ?? sql.param(col.onUpdateFn!(), col);\n\t\t\tconst res = sql`${sql.identifier(this.casing.getColumnCasing(col))} = ${value}`;\n\n\t\t\tif (i < setSize - 1) {\n\t\t\t\treturn [res, sql.raw(', ')];\n\t\t\t}\n\t\t\treturn [res];\n\t\t}));\n\t}\n\n\tbuildUpdateQuery({ table, set, where, returning, withList, from, joins }: PgUpdateConfig): SQL {\n\t\tconst withSql = this.buildWithCTE(withList);\n\n\t\tconst tableName = table[PgTable.Symbol.Name];\n\t\tconst tableSchema = table[PgTable.Symbol.Schema];\n\t\tconst origTableName = table[PgTable.Symbol.OriginalName];\n\t\tconst alias = tableName === origTableName ? undefined : tableName;\n\t\tconst tableSql = sql`${tableSchema ? sql`${sql.identifier(tableSchema)}.` : undefined}${\n\t\t\tsql.identifier(origTableName)\n\t\t}${alias && sql` ${sql.identifier(alias)}`}`;\n\n\t\tconst setSql = this.buildUpdateSet(table, set);\n\n\t\tconst fromSql = from && sql.join([sql.raw(' from '), this.buildFromTable(from)]);\n\n\t\tconst joinsSql = this.buildJoins(joins);\n\n\t\tconst returningSql = returning\n\t\t\t? sql` returning ${this.buildSelection(returning, { isSingleTable: !from })}`\n\t\t\t: undefined;\n\n\t\tconst whereSql = where ? sql` where ${where}` : undefined;\n\n\t\treturn sql`${withSql}update ${tableSql} set ${setSql}${fromSql}${joinsSql}${whereSql}${returningSql}`;\n\t}\n\n\t/**\n\t * Builds selection SQL with provided fields/expressions\n\t *\n\t * Examples:\n\t *\n\t * `select <selection> from`\n\t *\n\t * `insert ... returning <selection>`\n\t *\n\t * If `isSingleTable` is true, then columns won't be prefixed with table name\n\t */\n\tprivate buildSelection(\n\t\tfields: SelectedFieldsOrdered,\n\t\t{ isSingleTable = false }: { isSingleTable?: boolean } = {},\n\t): SQL {\n\t\tconst columnsLen = fields.length;\n\n\t\tconst chunks = fields\n\t\t\t.flatMap(({ field }, i) => {\n\t\t\t\tconst chunk: SQLChunk[] = [];\n\n\t\t\t\tif (is(field, SQL.Aliased) && field.isSelectionField) {\n\t\t\t\t\tchunk.push(sql.identifier(field.fieldAlias));\n\t\t\t\t} else if (is(field, SQL.Aliased) || is(field, SQL)) {\n\t\t\t\t\tconst query = is(field, SQL.Aliased) ? field.sql : field;\n\n\t\t\t\t\tif (isSingleTable) {\n\t\t\t\t\t\tchunk.push(\n\t\t\t\t\t\t\tnew SQL(\n\t\t\t\t\t\t\t\tquery.queryChunks.map((c) => {\n\t\t\t\t\t\t\t\t\tif (is(c, PgColumn)) {\n\t\t\t\t\t\t\t\t\t\treturn sql.identifier(this.casing.getColumnCasing(c));\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\treturn c;\n\t\t\t\t\t\t\t\t}),\n\t\t\t\t\t\t\t),\n\t\t\t\t\t\t);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tchunk.push(query);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (is(field, SQL.Aliased)) {\n\t\t\t\t\t\tchunk.push(sql` as ${sql.identifier(field.fieldAlias)}`);\n\t\t\t\t\t}\n\t\t\t\t} else if (is(field, Column)) {\n\t\t\t\t\tif (isSingleTable) {\n\t\t\t\t\t\tchunk.push(sql.identifier(this.casing.getColumnCasing(field)));\n\t\t\t\t\t} else {\n\t\t\t\t\t\tchunk.push(field);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (i < columnsLen - 1) {\n\t\t\t\t\tchunk.push(sql`, `);\n\t\t\t\t}\n\n\t\t\t\treturn chunk;\n\t\t\t});\n\n\t\treturn sql.join(chunks);\n\t}\n\n\tprivate buildJoins(joins: PgSelectJoinConfig[] | undefined): SQL | undefined {\n\t\tif (!joins || joins.length === 0) {\n\t\t\treturn undefined;\n\t\t}\n\n\t\tconst joinsArray: SQL[] = [];\n\n\t\tfor (const [index, joinMeta] of joins.entries()) {\n\t\t\tif (index === 0) {\n\t\t\t\tjoinsArray.push(sql` `);\n\t\t\t}\n\t\t\tconst table = joinMeta.table;\n\t\t\tconst lateralSql = joinMeta.lateral ? sql` lateral` : undefined;\n\n\t\t\tif (is(table, PgTable)) {\n\t\t\t\tconst tableName = table[PgTable.Symbol.Name];\n\t\t\t\tconst tableSchema = table[PgTable.Symbol.Schema];\n\t\t\t\tconst origTableName = table[PgTable.Symbol.OriginalName];\n\t\t\t\tconst alias = tableName === origTableName ? undefined : joinMeta.alias;\n\t\t\t\tjoinsArray.push(\n\t\t\t\t\tsql`${sql.raw(joinMeta.joinType)} join${lateralSql} ${\n\t\t\t\t\t\ttableSchema ? sql`${sql.identifier(tableSchema)}.` : undefined\n\t\t\t\t\t}${sql.identifier(origTableName)}${alias && sql` ${sql.identifier(alias)}`} on ${joinMeta.on}`,\n\t\t\t\t);\n\t\t\t} else if (is(table, View)) {\n\t\t\t\tconst viewName = table[ViewBaseConfig].name;\n\t\t\t\tconst viewSchema = table[ViewBaseConfig].schema;\n\t\t\t\tconst origViewName = table[ViewBaseConfig].originalName;\n\t\t\t\tconst alias = viewName === origViewName ? undefined : joinMeta.alias;\n\t\t\t\tjoinsArray.push(\n\t\t\t\t\tsql`${sql.raw(joinMeta.joinType)} join${lateralSql} ${\n\t\t\t\t\t\tviewSchema ? sql`${sql.identifier(viewSchema)}.` : undefined\n\t\t\t\t\t}${sql.identifier(origViewName)}${alias && sql` ${sql.identifier(alias)}`} on ${joinMeta.on}`,\n\t\t\t\t);\n\t\t\t} else {\n\t\t\t\tjoinsArray.push(\n\t\t\t\t\tsql`${sql.raw(joinMeta.joinType)} join${lateralSql} ${table} on ${joinMeta.on}`,\n\t\t\t\t);\n\t\t\t}\n\t\t\tif (index < joins.length - 1) {\n\t\t\t\tjoinsArray.push(sql` `);\n\t\t\t}\n\t\t}\n\n\t\treturn sql.join(joinsArray);\n\t}\n\n\tprivate buildFromTable(\n\t\ttable: SQL | Subquery | PgViewBase | PgTable | undefined,\n\t): SQL | Subquery | PgViewBase | PgTable | undefined {\n\t\tif (is(table, Table) && table[Table.Symbol.OriginalName] !== table[Table.Symbol.Name]) {\n\t\t\tlet fullName = sql`${sql.identifier(table[Table.Symbol.OriginalName])}`;\n\t\t\tif (table[Table.Symbol.Schema]) {\n\t\t\t\tfullName = sql`${sql.identifier(table[Table.Symbol.Schema]!)}.${fullName}`;\n\t\t\t}\n\t\t\treturn sql`${fullName} ${sql.identifier(table[Table.Symbol.Name])}`;\n\t\t}\n\n\t\treturn table;\n\t}\n\n\tbuildSelectQuery(\n\t\t{\n\t\t\twithList,\n\t\t\tfields,\n\t\t\tfieldsFlat,\n\t\t\twhere,\n\t\t\thaving,\n\t\t\ttable,\n\t\t\tjoins,\n\t\t\torderBy,\n\t\t\tgroupBy,\n\t\t\tlimit,\n\t\t\toffset,\n\t\t\tlockingClause,\n\t\t\tdistinct,\n\t\t\tsetOperators,\n\t\t}: PgSelectConfig,\n\t): SQL {\n\t\tconst fieldsList = fieldsFlat ?? orderSelectedFields<PgColumn>(fields);\n\t\tfor (const f of fieldsList) {\n\t\t\tif (\n\t\t\t\tis(f.field, Column)\n\t\t\t\t&& getTableName(f.field.table)\n\t\t\t\t\t!== (is(table, Subquery)\n\t\t\t\t\t\t? table._.alias\n\t\t\t\t\t\t: is(table, PgViewBase)\n\t\t\t\t\t\t? table[ViewBaseConfig].name\n\t\t\t\t\t\t: is(table, SQL)\n\t\t\t\t\t\t? undefined\n\t\t\t\t\t\t: getTableName(table))\n\t\t\t\t&& !((table) =>\n\t\t\t\t\tjoins?.some(({ alias }) =>\n\t\t\t\t\t\talias === (table[Table.Symbol.IsAlias] ? getTableName(table) : table[Table.Symbol.BaseName])\n\t\t\t\t\t))(f.field.table)\n\t\t\t) {\n\t\t\t\tconst tableName = getTableName(f.field.table);\n\t\t\t\tthrow new Error(\n\t\t\t\t\t`Your \"${\n\t\t\t\t\t\tf.path.join('->')\n\t\t\t\t\t}\" field references a column \"${tableName}\".\"${f.field.name}\", but the table \"${tableName}\" is not part of the query! Did you forget to join it?`,\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\n\t\tconst isSingleTable = !joins || joins.length === 0;\n\n\t\tconst withSql = this.buildWithCTE(withList);\n\n\t\tlet distinctSql: SQL | undefined;\n\t\tif (distinct) {\n\t\t\tdistinctSql = distinct === true ? sql` distinct` : sql` distinct on (${sql.join(distinct.on, sql`, `)})`;\n\t\t}\n\n\t\tconst selection = this.buildSelection(fieldsList, { isSingleTable });\n\n\t\tconst tableSql = this.buildFromTable(table);\n\n\t\tconst joinsSql = this.buildJoins(joins);\n\n\t\tconst whereSql = where ? sql` where ${where}` : undefined;\n\n\t\tconst havingSql = having ? sql` having ${having}` : undefined;\n\n\t\tlet orderBySql;\n\t\tif (orderBy && orderBy.length > 0) {\n\t\t\torderBySql = sql` order by ${sql.join(orderBy, sql`, `)}`;\n\t\t}\n\n\t\tlet groupBySql;\n\t\tif (groupBy && groupBy.length > 0) {\n\t\t\tgroupBySql = sql` group by ${sql.join(groupBy, sql`, `)}`;\n\t\t}\n\n\t\tconst limitSql = typeof limit === 'object' || (typeof limit === 'number' && limit >= 0)\n\t\t\t? sql` limit ${limit}`\n\t\t\t: undefined;\n\n\t\tconst offsetSql = offset ? sql` offset ${offset}` : undefined;\n\n\t\tconst lockingClauseSql = sql.empty();\n\t\tif (lockingClause) {\n\t\t\tconst clauseSql = sql` for ${sql.raw(lockingClause.strength)}`;\n\t\t\tif (lockingClause.config.of) {\n\t\t\t\tclauseSql.append(\n\t\t\t\t\tsql` of ${\n\t\t\t\t\t\tsql.join(\n\t\t\t\t\t\t\tArray.isArray(lockingClause.config.of) ? lockingClause.config.of : [lockingClause.config.of],\n\t\t\t\t\t\t\tsql`, `,\n\t\t\t\t\t\t)\n\t\t\t\t\t}`,\n\t\t\t\t);\n\t\t\t}\n\t\t\tif (lockingClause.config.noWait) {\n\t\t\t\tclauseSql.append(sql` no wait`);\n\t\t\t} else if (lockingClause.config.skipLocked) {\n\t\t\t\tclauseSql.append(sql` skip locked`);\n\t\t\t}\n\t\t\tlockingClauseSql.append(clauseSql);\n\t\t}\n\t\tconst finalQuery =\n\t\t\tsql`${withSql}select${distinctSql} ${selection} from ${tableSql}${joinsSql}${whereSql}${groupBySql}${havingSql}${orderBySql}${limitSql}${offsetSql}${lockingClauseSql}`;\n\n\t\tif (setOperators.length > 0) {\n\t\t\treturn this.buildSetOperations(finalQuery, setOperators);\n\t\t}\n\n\t\treturn finalQuery;\n\t}\n\n\tbuildSetOperations(leftSelect: SQL, setOperators: PgSelectConfig['setOperators']): SQL {\n\t\tconst [setOperator, ...rest] = setOperators;\n\n\t\tif (!setOperator) {\n\t\t\tthrow new Error('Cannot pass undefined values to any set operator');\n\t\t}\n\n\t\tif (rest.length === 0) {\n\t\t\treturn this.buildSetOperationQuery({ leftSelect, setOperator });\n\t\t}\n\n\t\t// Some recursive magic here\n\t\treturn this.buildSetOperations(\n\t\t\tthis.buildSetOperationQuery({ leftSelect, setOperator }),\n\t\t\trest,\n\t\t);\n\t}\n\n\tbuildSetOperationQuery({\n\t\tleftSelect,\n\t\tsetOperator: { type, isAll, rightSelect, limit, orderBy, offset },\n\t}: { leftSelect: SQL; setOperator: PgSelectConfig['setOperators'][number] }): SQL {\n\t\tconst leftChunk = sql`(${leftSelect.getSQL()}) `;\n\t\tconst rightChunk = sql`(${rightSelect.getSQL()})`;\n\n\t\tlet orderBySql;\n\t\tif (orderBy && orderBy.length > 0) {\n\t\t\tconst orderByValues: (SQL<unknown> | Name)[] = [];\n\n\t\t\t// The next bit is necessary because the sql operator replaces ${table.column} with `table`.`column`\n\t\t\t// which is invalid Sql syntax, Table from one of the SELECTs cannot be used in global ORDER clause\n\t\t\tfor (const singleOrderBy of orderBy) {\n\t\t\t\tif (is(singleOrderBy, PgColumn)) {\n\t\t\t\t\torderByValues.push(sql.identifier(singleOrderBy.name));\n\t\t\t\t} else if (is(singleOrderBy, SQL)) {\n\t\t\t\t\tfor (let i = 0; i < singleOrderBy.queryChunks.length; i++) {\n\t\t\t\t\t\tconst chunk = singleOrderBy.queryChunks[i];\n\n\t\t\t\t\t\tif (is(chunk, PgColumn)) {\n\t\t\t\t\t\t\tsingleOrderBy.queryChunks[i] = sql.identifier(chunk.name);\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\n\t\t\t\t\torderByValues.push(sql`${singleOrderBy}`);\n\t\t\t\t} else {\n\t\t\t\t\torderByValues.push(sql`${singleOrderBy}`);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\torderBySql = sql` order by ${sql.join(orderByValues, sql`, `)} `;\n\t\t}\n\n\t\tconst limitSql = typeof limit === 'object' || (typeof limit === 'number' && limit >= 0)\n\t\t\t? sql` limit ${limit}`\n\t\t\t: undefined;\n\n\t\tconst operatorChunk = sql.raw(`${type} ${isAll ? 'all ' : ''}`);\n\n\t\tconst offsetSql = offset ? sql` offset ${offset}` : undefined;\n\n\t\treturn sql`${leftChunk}${operatorChunk}${rightChunk}${orderBySql}${limitSql}${offsetSql}`;\n\t}\n\n\tbuildInsertQuery(\n\t\t{ table, values: valuesOrSelect, onConflict, returning, withList, select, overridingSystemValue_ }: PgInsertConfig,\n\t): SQL {\n\t\tconst valuesSqlList: ((SQLChunk | SQL)[] | SQL)[] = [];\n\t\tconst columns: Record<string, PgColumn> = table[Table.Symbol.Columns];\n\n\t\tconst colEntries: [string, PgColumn][] = Object.entries(columns).filter(([_, col]) => !col.shouldDisableInsert());\n\n\t\tconst insertOrder = colEntries.map(\n\t\t\t([, column]) => sql.identifier(this.casing.getColumnCasing(column)),\n\t\t);\n\n\t\tif (select) {\n\t\t\tconst select = valuesOrSelect as AnyPgSelectQueryBuilder | SQL;\n\n\t\t\tif (is(select, SQL)) {\n\t\t\t\tvaluesSqlList.push(select);\n\t\t\t} else {\n\t\t\t\tvaluesSqlList.push(select.getSQL());\n\t\t\t}\n\t\t} else {\n\t\t\tconst values = valuesOrSelect as Record<string, Param | SQL>[];\n\t\t\tvaluesSqlList.push(sql.raw('values '));\n\n\t\t\tfor (const [valueIndex, value] of values.entries()) {\n\t\t\t\tconst valueList: (SQLChunk | SQL)[] = [];\n\t\t\t\tfor (const [fieldName, col] of colEntries) {\n\t\t\t\t\tconst colValue = value[fieldName];\n\t\t\t\t\tif (colValue === undefined || (is(colValue, Param) && colValue.value === undefined)) {\n\t\t\t\t\t\t// eslint-disable-next-line unicorn/no-negated-condition\n\t\t\t\t\t\tif (col.defaultFn !== undefined) {\n\t\t\t\t\t\t\tconst defaultFnResult = col.defaultFn();\n\t\t\t\t\t\t\tconst defaultValue = is(defaultFnResult, SQL) ? defaultFnResult : sql.param(defaultFnResult, col);\n\t\t\t\t\t\t\tvalueList.push(defaultValue);\n\t\t\t\t\t\t\t// eslint-disable-next-line unicorn/no-negated-condition\n\t\t\t\t\t\t} else if (!col.default && col.onUpdateFn !== undefined) {\n\t\t\t\t\t\t\tconst onUpdateFnResult = col.onUpdateFn();\n\t\t\t\t\t\t\tconst newValue = is(onUpdateFnResult, SQL) ? onUpdateFnResult : sql.param(onUpdateFnResult, col);\n\t\t\t\t\t\t\tvalueList.push(newValue);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tvalueList.push(sql`default`);\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tvalueList.push(colValue);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tvaluesSqlList.push(valueList);\n\t\t\t\tif (valueIndex < values.length - 1) {\n\t\t\t\t\tvaluesSqlList.push(sql`, `);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tconst withSql = this.buildWithCTE(withList);\n\n\t\tconst valuesSql = sql.join(valuesSqlList);\n\n\t\tconst returningSql = returning\n\t\t\t? sql` returning ${this.buildSelection(returning, { isSingleTable: true })}`\n\t\t\t: undefined;\n\n\t\tconst onConflictSql = onConflict ? sql` on conflict ${onConflict}` : undefined;\n\n\t\tconst overridingSql = overridingSystemValue_ === true ? sql`overriding system value ` : undefined;\n\n\t\treturn sql`${withSql}insert into ${table} ${insertOrder} ${overridingSql}${valuesSql}${onConflictSql}${returningSql}`;\n\t}\n\n\tbuildRefreshMaterializedViewQuery(\n\t\t{ view, concurrently, withNoData }: { view: PgMaterializedView; concurrently?: boolean; withNoData?: boolean },\n\t): SQL {\n\t\tconst concurrentlySql = concurrently ? sql` concurrently` : undefined;\n\t\tconst withNoDataSql = withNoData ? sql` with no data` : undefined;\n\n\t\treturn sql`refresh materialized view${concurrentlySql} ${view}${withNoDataSql}`;\n\t}\n\n\tprepareTyping(encoder: DriverValueEncoder<unknown, unknown>): QueryTypingsValue {\n\t\tif (is(encoder, PgJsonb) || is(encoder, PgJson)) {\n\t\t\treturn 'json';\n\t\t} else if (is(encoder, PgNumeric)) {\n\t\t\treturn 'decimal';\n\t\t} else if (is(encoder, PgTime)) {\n\t\t\treturn 'time';\n\t\t} else if (is(encoder, PgTimestamp) || is(encoder, PgTimestampString)) {\n\t\t\treturn 'timestamp';\n\t\t} else if (is(encoder, PgDate) || is(encoder, PgDateString)) {\n\t\t\treturn 'date';\n\t\t} else if (is(encoder, PgUUID)) {\n\t\t\treturn 'uuid';\n\t\t} else {\n\t\t\treturn 'none';\n\t\t}\n\t}\n\n\tsqlToQuery(sql: SQL, invokeSource?: 'indexes' | undefined): QueryWithTypings {\n\t\treturn sql.toQuery({\n\t\t\tcasing: this.casing,\n\t\t\tescapeName: this.escapeName,\n\t\t\tescapeParam: this.escapeParam,\n\t\t\tescapeString: this.escapeString,\n\t\t\tprepareTyping: this.prepareTyping,\n\t\t\tinvokeSource,\n\t\t});\n\t}\n\n\t// buildRelationalQueryWithPK({\n\t// \tfullSchema,\n\t// \tschema,\n\t// \ttableNamesMap,\n\t// \ttable,\n\t// \ttableConfig,\n\t// \tqueryConfig: config,\n\t// \ttableAlias,\n\t// \tisRoot = false,\n\t// \tjoinOn,\n\t// }: {\n\t// \tfullSchema: Record<string, unknown>;\n\t// \tschema: TablesRelationalConfig;\n\t// \ttableNamesMap: Record<string, string>;\n\t// \ttable: PgTable;\n\t// \ttableConfig: TableRelationalConfig;\n\t// \tqueryConfig: true | DBQueryConfig<'many', true>;\n\t// \ttableAlias: string;\n\t// \tisRoot?: boolean;\n\t// \tjoinOn?: SQL;\n\t// }): BuildRelationalQueryResult<PgTable, PgColumn> {\n\t// \t// For { \"<relation>\": true }, return a table with selection of all columns\n\t// \tif (config === true) {\n\t// \t\tconst selectionEntries = Object.entries(tableConfig.columns);\n\t// \t\tconst selection: BuildRelationalQueryResult<PgTable, PgColumn>['selection'] = selectionEntries.map((\n\t// \t\t\t[key, value],\n\t// \t\t) => ({\n\t// \t\t\tdbKey: value.name,\n\t// \t\t\ttsKey: key,\n\t// \t\t\tfield: value as PgColumn,\n\t// \t\t\trelationTableTsKey: undefined,\n\t// \t\t\tisJson: false,\n\t// \t\t\tselection: [],\n\t// \t\t}));\n\n\t// \t\treturn {\n\t// \t\t\ttableTsKey: tableConfig.tsName,\n\t// \t\t\tsql: table,\n\t// \t\t\tselection,\n\t// \t\t};\n\t// \t}\n\n\t// \t// let selection: BuildRelationalQueryResult<PgTable, PgColumn>['selection'] = [];\n\t// \t// let selectionForBuild = selection;\n\n\t// \tconst aliasedColumns = Object.fromEntries(\n\t// \t\tObject.entries(tableConfig.columns).map(([key, value]) => [key, aliasedTableColumn(value, tableAlias)]),\n\t// \t);\n\n\t// \tconst aliasedRelations = Object.fromEntries(\n\t// \t\tObject.entries(tableConfig.relations).map(([key, value]) => [key, aliasedRelation(value, tableAlias)]),\n\t// \t);\n\n\t// \tconst aliasedFields = Object.assign({}, aliasedColumns, aliasedRelations);\n\n\t// \tlet where, hasUserDefinedWhere;\n\t// \tif (config.where) {\n\t// \t\tconst whereSql = typeof config.where === 'function' ? config.where(aliasedFields, operators) : config.where;\n\t// \t\twhere = whereSql && mapColumnsInSQLToAlias(whereSql, tableAlias);\n\t// \t\thasUserDefinedWhere = !!where;\n\t// \t}\n\t// \twhere = and(joinOn, where);\n\n\t// \t// const fieldsSelection: { tsKey: string; value: PgColumn | SQL.Aliased; isExtra?: boolean }[] = [];\n\t// \tlet joins: Join[] = [];\n\t// \tlet selectedColumns: string[] = [];\n\n\t// \t// Figure out which columns to select\n\t// \tif (config.columns) {\n\t// \t\tlet isIncludeMode = false;\n\n\t// \t\tfor (const [field, value] of Object.entries(config.columns)) {\n\t// \t\t\tif (value === undefined) {\n\t// \t\t\t\tcontinue;\n\t// \t\t\t}\n\n\t// \t\t\tif (field in tableConfig.columns) {\n\t// \t\t\t\tif (!isIncludeMode && value === true) {\n\t// \t\t\t\t\tisIncludeMode = true;\n\t// \t\t\t\t}\n\t// \t\t\t\tselectedColumns.push(field);\n\t// \t\t\t}\n\t// \t\t}\n\n\t// \t\tif (selectedColumns.length > 0) {\n\t// \t\t\tselectedColumns = isIncludeMode\n\t// \t\t\t\t? selectedColumns.filter((c) => config.columns?.[c] === true)\n\t// \t\t\t\t: Object.keys(tableConfig.columns).filter((key) => !selectedColumns.includes(key));\n\t// \t\t}\n\t// \t} else {\n\t// \t\t// Select all columns if selection is not specified\n\t// \t\tselectedColumns = Object.keys(tableConfig.columns);\n\t// \t}\n\n\t// \t// for (const field of selectedColumns) {\n\t// \t// \tconst column = tableConfig.columns[field]! as PgColumn;\n\t// \t// \tfieldsSelection.push({ tsKey: field, value: column });\n\t// \t// }\n\n\t// \tlet initiallySelectedRelations: {\n\t// \t\ttsKey: string;\n\t// \t\tqueryConfig: true | DBQueryConfig<'many', false>;\n\t// \t\trelation: Relation;\n\t// \t}[] = [];\n\n\t// \t// let selectedRelations: BuildRelationalQueryResult<PgTable, PgColumn>['selection'] = [];\n\n\t// \t// Figure out which relations to select\n\t// \tif (config.with) {\n\t// \t\tinitiallySelectedRelations = Object.entries(config.with)\n\t// \t\t\t.filter((entry): entry is [typeof entry[0], NonNullable<typeof entry[1]>] => !!entry[1])\n\t// \t\t\t.map(([tsKey, queryConfig]) => ({ tsKey, queryConfig, relation: tableConfig.relations[tsKey]! }));\n\t// \t}\n\n\t// \tconst manyRelations = initiallySelectedRelations.filter((r) =>\n\t// \t\tis(r.relation, Many)\n\t// \t\t&& (schema[tableNamesMap[r.relation.referencedTable[Table.Symbol.Name]]!]?.primaryKey.length ?? 0) > 0\n\t// \t);\n\t// \t// If this is the last Many relation (or there are no Many relations), we are on the innermost subquery level\n\t// \tconst isInnermostQuery = manyRelations.length < 2;\n\n\t// \tconst selectedExtras: {\n\t// \t\ttsKey: string;\n\t// \t\tvalue: SQL.Aliased;\n\t// \t}[] = [];\n\n\t// \t// Figure out which extras to select\n\t// \tif (isInnermostQuery && config.extras) {\n\t// \t\tconst extras = typeof config.extras === 'function'\n\t// \t\t\t? config.extras(aliasedFields, { sql })\n\t// \t\t\t: config.extras;\n\t// \t\tfor (const [tsKey, value] of Object.entries(extras)) {\n\t// \t\t\tselectedExtras.push({\n\t// \t\t\t\ttsKey,\n\t// \t\t\t\tvalue: mapColumnsInAliasedSQLToAlias(value, tableAlias),\n\t// \t\t\t});\n\t// \t\t}\n\t// \t}\n\n\t// \t// Transform `fieldsSelection` into `selection`\n\t// \t// `fieldsSelection` shouldn't be used after this point\n\t// \t// for (const { tsKey, value, isExtra } of fieldsSelection) {\n\t// \t// \tselection.push({\n\t// \t// \t\tdbKey: is(value, SQL.Aliased) ? value.fieldAlias : tableConfig.columns[tsKey]!.name,\n\t// \t// \t\ttsKey,\n\t// \t// \t\tfield: is(value, Column) ? aliasedTableColumn(value, tableAlias) : value,\n\t// \t// \t\trelationTableTsKey: undefined,\n\t// \t// \t\tisJson: false,\n\t// \t// \t\tisExtra,\n\t// \t// \t\tselection: [],\n\t// \t// \t});\n\t// \t// }\n\n\t// \tlet orderByOrig = typeof config.orderBy === 'function'\n\t// \t\t? config.orderBy(aliasedFields, orderByOperators)\n\t// \t\t: config.orderBy ?? [];\n\t// \tif (!Array.isArray(orderByOrig)) {\n\t// \t\torderByOrig = [orderByOrig];\n\t// \t}\n\t// \tconst orderBy = orderByOrig.map((orderByValue) => {\n\t// \t\tif (is(orderByValue, Column)) {\n\t// \t\t\treturn aliasedTableColumn(orderByValue, tableAlias) as PgColumn;\n\t// \t\t}\n\t// \t\treturn mapColumnsInSQLToAlias(orderByValue, tableAlias);\n\t// \t});\n\n\t// \tconst limit = isInnermostQuery ? config.limit : undefined;\n\t// \tconst offset = isInnermostQuery ? config.offset : undefined;\n\n\t// \t// For non-root queries without additional config except columns, return a table with selection\n\t// \tif (\n\t// \t\t!isRoot\n\t// \t\t&& initiallySelectedRelations.length === 0\n\t// \t\t&& selectedExtras.length === 0\n\t// \t\t&& !where\n\t// \t\t&& orderBy.length === 0\n\t// \t\t&& limit === undefined\n\t// \t\t&& offset === undefined\n\t// \t) {\n\t// \t\treturn {\n\t// \t\t\ttableTsKey: tableConfig.tsName,\n\t// \t\t\tsql: table,\n\t// \t\t\tselection: selectedColumns.map((key) => ({\n\t// \t\t\t\tdbKey: tableConfig.columns[key]!.name,\n\t// \t\t\t\ttsKey: key,\n\t// \t\t\t\tfield: tableConfig.columns[key] as PgColumn,\n\t// \t\t\t\trelationTableTsKey: undefined,\n\t// \t\t\t\tisJson: false,\n\t// \t\t\t\tselection: [],\n\t// \t\t\t})),\n\t// \t\t};\n\t// \t}\n\n\t// \tconst selectedRelationsWithoutPK:\n\n\t// \t// Process all relations without primary keys, because they need to be joined differently and will all be on the same query level\n\t// \tfor (\n\t// \t\tconst {\n\t// \t\t\ttsKey: selectedRelationTsKey,\n\t// \t\t\tqueryConfig: selectedRelationConfigValue,\n\t// \t\t\trelation,\n\t// \t\t} of initiallySelectedRelations\n\t// \t) {\n\t// \t\tconst normalizedRelation = normalizeRelation(schema, tableNamesMap, relation);\n\t// \t\tconst relationTableName = relation.referencedTable[Table.Symbol.Name];\n\t// \t\tconst relationTableTsName = tableNamesMap[relationTableName]!;\n\t// \t\tconst relationTable = schema[relationTableTsName]!;\n\n\t// \t\tif (relationTable.primaryKey.length > 0) {\n\t// \t\t\tcontinue;\n\t// \t\t}\n\n\t// \t\tconst relationTableAlias = `${tableAlias}_${selectedRelationTsKey}`;\n\t// \t\tconst joinOn = and(\n\t// \t\t\t...normalizedRelation.fields.map((field, i) =>\n\t// \t\t\t\teq(\n\t// \t\t\t\t\taliasedTableColumn(normalizedRelation.references[i]!, relationTableAlias),\n\t// \t\t\t\t\taliasedTableColumn(field, tableAlias),\n\t// \t\t\t\t)\n\t// \t\t\t),\n\t// \t\t);\n\t// \t\tconst builtRelation = this.buildRelationalQueryWithoutPK({\n\t// \t\t\tfullSchema,\n\t// \t\t\tschema,\n\t// \t\t\ttableNamesMap,\n\t// \t\t\ttable: fullSchema[relationTableTsName] as PgTable,\n\t// \t\t\ttableConfig: schema[relationTableTsName]!,\n\t// \t\t\tqueryConfig: selectedRelationConfigValue,\n\t// \t\t\ttableAlias: relationTableAlias,\n\t// \t\t\tjoinOn,\n\t// \t\t\tnestedQueryRelation: relation,\n\t// \t\t});\n\t// \t\tconst field = sql`${sql.identifier(relationTableAlias)}.${sql.identifier('data')}`.as(selectedRelationTsKey);\n\t// \t\tjoins.push({\n\t// \t\t\ton: sql`true`,\n\t// \t\t\ttable: new Subquery(builtRelation.sql as SQL, {}, relationTableAlias),\n\t// \t\t\talias: relationTableAlias,\n\t// \t\t\tjoinType: 'left',\n\t// \t\t\tlateral: true,\n\t// \t\t});\n\t// \t\tselectedRelations.push({\n\t// \t\t\tdbKey: selectedRelationTsKey,\n\t// \t\t\ttsKey: selectedRelationTsKey,\n\t// \t\t\tfield,\n\t// \t\t\trelationTableTsKey: relationTableTsName,\n\t// \t\t\tisJson: true,\n\t// \t\t\tselection: builtRelation.selection,\n\t// \t\t});\n\t// \t}\n\n\t// \tconst oneRelations = initiallySelectedRelations.filter((r): r is typeof r & { relation: One } =>\n\t// \t\tis(r.relation, One)\n\t// \t);\n\n\t// \t// Process all One relations with PKs, because they can all be joined on the same level\n\t// \tfor (\n\t// \t\tconst {\n\t// \t\t\ttsKey: selectedRelationTsKey,\n\t// \t\t\tqueryConfig: selectedRelationConfigValue,\n\t// \t\t\trelation,\n\t// \t\t} of oneRelations\n\t// \t) {\n\t// \t\tconst normalizedRelation = normalizeRelation(schema, tableNamesMap, relation);\n\t// \t\tconst relationTableName = relation.referencedTable[Table.Symbol.Name];\n\t// \t\tconst relationTableTsName = tableNamesMap[relationTableName]!;\n\t// \t\tconst relationTableAlias = `${tableAlias}_${selectedRelationTsKey}`;\n\t// \t\tconst relationTable = schema[relationTableTsName]!;\n\n\t// \t\tif (relationTable.primaryKey.length === 0) {\n\t// \t\t\tcontinue;\n\t// \t\t}\n\n\t// \t\tconst joinOn = and(\n\t// \t\t\t...normalizedRelation.fields.map((field, i) =>\n\t// \t\t\t\teq(\n\t// \t\t\t\t\taliasedTableColumn(normalizedRelation.references[i]!, relationTableAlias),\n\t// \t\t\t\t\taliasedTableColumn(field, tableAlias),\n\t// \t\t\t\t)\n\t// \t\t\t),\n\t// \t\t);\n\t// \t\tconst builtRelation = this.buildRelationalQueryWithPK({\n\t// \t\t\tfullSchema,\n\t// \t\t\tschema,\n\t// \t\t\ttableNamesMap,\n\t// \t\t\ttable: fullSchema[relationTableTsName] as PgTable,\n\t// \t\t\ttableConfig: schema[relationTableTsName]!,\n\t// \t\t\tqueryConfig: selectedRelationConfigValue,\n\t// \t\t\ttableAlias: relationTableAlias,\n\t// \t\t\tjoinOn,\n\t// \t\t});\n\t// \t\tconst field = sql`case when ${sql.identifier(relationTableAlias)} is null then null else json_build_array(${\n\t// \t\t\tsql.join(\n\t// \t\t\t\tbuiltRelation.selection.map(({ field }) =>\n\t// \t\t\t\t\tis(field, SQL.Aliased)\n\t// \t\t\t\t\t\t? sql`${sql.identifier(relationTableAlias)}.${sql.identifier(field.fieldAlias)}`\n\t// \t\t\t\t\t\t: is(field, Column)\n\t// \t\t\t\t\t\t? aliasedTableColumn(field, relationTableAlias)\n\t// \t\t\t\t\t\t: field\n\t// \t\t\t\t),\n\t// \t\t\t\tsql`, `,\n\t// \t\t\t)\n\t// \t\t}) end`.as(selectedRelationTsKey);\n\t// \t\tconst isLateralJoin = is(builtRelation.sql, SQL);\n\t// \t\tjoins.push({\n\t// \t\t\ton: isLateralJoin ? sql`true` : joinOn,\n\t// \t\t\ttable: is(builtRelation.sql, SQL)\n\t// \t\t\t\t? new Subquery(builtRelation.sql, {}, relationTableAlias)\n\t// \t\t\t\t: aliasedTable(builtRelation.sql, relationTableAlias),\n\t// \t\t\talias: relationTableAlias,\n\t// \t\t\tjoinType: 'left',\n\t// \t\t\tlateral: is(builtRelation.sql, SQL),\n\t// \t\t});\n\t// \t\tselectedRelations.push({\n\t// \t\t\tdbKey: selectedRelationTsKey,\n\t// \t\t\ttsKey: selectedRelationTsKey,\n\t// \t\t\tfield,\n\t// \t\t\trelationTableTsKey: relationTableTsName,\n\t// \t\t\tisJson: true,\n\t// \t\t\tselection: builtRelation.selection,\n\t// \t\t});\n\t// \t}\n\n\t// \tlet distinct: PgSelectConfig['distinct'];\n\t// \tlet tableFrom: PgTable | Subquery = table;\n\n\t// \t// Process first Many relation - each one requires a nested subquery\n\t// \tconst manyRelation = manyRelations[0];\n\t// \tif (manyRelation) {\n\t// \t\tconst {\n\t// \t\t\ttsKey: selectedRelationTsKey,\n\t// \t\t\tqueryConfig: selectedRelationQueryConfig,\n\t// \t\t\trelation,\n\t// \t\t} = manyRelation;\n\n\t// \t\tdistinct = {\n\t// \t\t\ton: tableConfig.primaryKey.map((c) => aliasedTableColumn(c as PgColumn, tableAlias)),\n\t// \t\t};\n\n\t// \t\tconst normalizedRelation = normalizeRelation(schema, tableNamesMap, relation);\n\t// \t\tconst relationTableName = relation.referencedTable[Table.Symbol.Name];\n\t// \t\tconst relationTableTsName = tableNamesMap[relationTableName]!;\n\t// \t\tconst relationTableAlias = `${tableAlias}_${selectedRelationTsKey}`;\n\t// \t\tconst joinOn = and(\n\t// \t\t\t...normalizedRelation.fields.map((field, i) =>\n\t// \t\t\t\teq(\n\t// \t\t\t\t\taliasedTableColumn(normalizedRelation.references[i]!, relationTableAlias),\n\t// \t\t\t\t\taliasedTableColumn(field, tableAlias),\n\t// \t\t\t\t)\n\t// \t\t\t),\n\t// \t\t);\n\n\t// \t\tconst builtRelationJoin = this.buildRelationalQueryWithPK({\n\t// \t\t\tfullSchema,\n\t// \t\t\tschema,\n\t// \t\t\ttableNamesMap,\n\t// \t\t\ttable: fullSchema[relationTableTsName] as PgTable,\n\t// \t\t\ttableConfig: schema[relationTableTsName]!,\n\t// \t\t\tqueryConfig: selectedRelationQueryConfig,\n\t// \t\t\ttableAlias: relationTableAlias,\n\t// \t\t\tjoinOn,\n\t// \t\t});\n\n\t// \t\tconst builtRelationSelectionField = sql`case when ${\n\t// \t\t\tsql.identifier(relationTableAlias)\n\t// \t\t} is null then '[]' else json_agg(json_build_array(${\n\t// \t\t\tsql.join(\n\t// \t\t\t\tbuiltRelationJoin.selection.map(({ field }) =>\n\t// \t\t\t\t\tis(field, SQL.Aliased)\n\t// \t\t\t\t\t\t? sql`${sql.identifier(relationTableAlias)}.${sql.identifier(field.fieldAlias)}`\n\t// \t\t\t\t\t\t: is(field, Column)\n\t// \t\t\t\t\t\t? aliasedTableColumn(field, relationTableAlias)\n\t// \t\t\t\t\t\t: field\n\t// \t\t\t\t),\n\t// \t\t\t\tsql`, `,\n\t// \t\t\t)\n\t// \t\t})) over (partition by ${sql.join(distinct.on, sql`, `)}) end`.as(selectedRelationTsKey);\n\t// \t\tconst isLateralJoin = is(builtRelationJoin.sql, SQL);\n\t// \t\tjoins.push({\n\t// \t\t\ton: isLateralJoin ? sql`true` : joinOn,\n\t// \t\t\ttable: isLateralJoin\n\t// \t\t\t\t? new Subquery(builtRelationJoin.sql as SQL, {}, relationTableAlias)\n\t// \t\t\t\t: aliasedTable(builtRelationJoin.sql as PgTable, relationTableAlias),\n\t// \t\t\talias: relationTableAlias,\n\t// \t\t\tjoinType: 'left',\n\t// \t\t\tlateral: isLateralJoin,\n\t// \t\t});\n\n\t// \t\t// Build the \"from\" subquery with the remaining Many relations\n\t// \t\tconst builtTableFrom = this.buildRelationalQueryWithPK({\n\t// \t\t\tfullSchema,\n\t// \t\t\tschema,\n\t// \t\t\ttableNamesMap,\n\t// \t\t\ttable,\n\t// \t\t\ttableConfig,\n\t// \t\t\tqueryConfig: {\n\t// \t\t\t\t...config,\n\t// \t\t\t\twhere: undefined,\n\t// \t\t\t\torderBy: undefined,\n\t// \t\t\t\tlimit: undefined,\n\t// \t\t\t\toffset: undefined,\n\t// \t\t\t\twith: manyRelations.slice(1).reduce<NonNullable<typeof config['with']>>(\n\t// \t\t\t\t\t(result, { tsKey, queryConfig: configValue }) => {\n\t// \t\t\t\t\t\tresult[tsKey] = configValue;\n\t// \t\t\t\t\t\treturn result;\n\t// \t\t\t\t\t},\n\t// \t\t\t\t\t{},\n\t// \t\t\t\t),\n\t// \t\t\t},\n\t// \t\t\ttableAlias,\n\t// \t\t});\n\n\t// \t\tselectedRelations.push({\n\t// \t\t\tdbKey: selectedRelationTsKey,\n\t// \t\t\ttsKey: selectedRelationTsKey,\n\t// \t\t\tfield: builtRelationSelectionField,\n\t// \t\t\trelationTableTsKey: relationTableTsName,\n\t// \t\t\tisJson: true,\n\t// \t\t\tselection: builtRelationJoin.selection,\n\t// \t\t});\n\n\t// \t\t// selection = builtTableFrom.selection.map((item) =>\n\t// \t\t// \tis(item.field, SQL.Aliased)\n\t// \t\t// \t\t? { ...item, field: sql`${sql.identifier(tableAlias)}.${sql.identifier(item.field.fieldAlias)}` }\n\t// \t\t// \t\t: item\n\t// \t\t// );\n\t// \t\t// selectionForBuild = [{\n\t// \t\t// \tdbKey: '*',\n\t// \t\t// \ttsKey: '*',\n\t// \t\t// \tfield: sql`${sql.identifier(tableAlias)}.*`,\n\t// \t\t// \tselection: [],\n\t// \t\t// \tisJson: false,\n\t// \t\t// \trelationTableTsKey: undefined,\n\t// \t\t// }];\n\t// \t\t// const newSelectionItem: (typeof selection)[number] = {\n\t// \t\t// \tdbKey: selectedRelationTsKey,\n\t// \t\t// \ttsKey: selectedRelationTsKey,\n\t// \t\t// \tfield,\n\t// \t\t// \trelationTableTsKey: relationTableTsName,\n\t// \t\t// \tisJson: true,\n\t// \t\t// \tselection: builtRelationJoin.selection,\n\t// \t\t// };\n\t// \t\t// selection.push(newSelectionItem);\n\t// \t\t// selectionForBuild.push(newSelectionItem);\n\n\t// \t\ttableFrom = is(builtTableFrom.sql, PgTable)\n\t// \t\t\t? builtTableFrom.sql\n\t// \t\t\t: new Subquery(builtTableFrom.sql, {}, tableAlias);\n\t// \t}\n\n\t// \tif (selectedColumns.length === 0 && selectedRelations.length === 0 && selectedExtras.length === 0) {\n\t// \t\tthrow new DrizzleError(`No fields selected for table \"${tableConfig.tsName}\" (\"${tableAlias}\")`);\n\t// \t}\n\n\t// \tlet selection: BuildRelationalQueryResult<PgTable, PgColumn>['selection'];\n\n\t// \tfunction prepareSelectedColumns() {\n\t// \t\treturn selectedColumns.map((key) => ({\n\t// \t\t\tdbKey: tableConfig.columns[key]!.name,\n\t// \t\t\ttsKey: key,\n\t// \t\t\tfield: tableConfig.columns[key] as PgColumn,\n\t// \t\t\trelationTableTsKey: undefined,\n\t// \t\t\tisJson: false,\n\t// \t\t\tselection: [],\n\t// \t\t}));\n\t// \t}\n\n\t// \tfunction prepareSelectedExtras() {\n\t// \t\treturn selectedExtras.map((item) => ({\n\t// \t\t\tdbKey: item.value.fieldAlias,\n\t// \t\t\ttsKey: item.tsKey,\n\t// \t\t\tfield: item.value,\n\t// \t\t\trelationTableTsKey: undefined,\n\t// \t\t\tisJson: false,\n\t// \t\t\tselection: [],\n\t// \t\t}));\n\t// \t}\n\n\t// \tif (isRoot) {\n\t// \t\tselection = [\n\t// \t\t\t...prepareSelectedColumns(),\n\t// \t\t\t...prepareSelectedExtras(),\n\t// \t\t];\n\t// \t}\n\n\t// \tif (hasUserDefinedWhere || orderBy.length > 0) {\n\t// \t\ttableFrom = new Subquery(\n\t// \t\t\tthis.buildSelectQuery({\n\t// \t\t\t\ttable: is(tableFrom, PgTable) ? aliasedTable(tableFrom, tableAlias) : tableFrom,\n\t// \t\t\t\tfields: {},\n\t// \t\t\t\tfieldsFlat: selectionForBuild.map(({ field }) => ({\n\t// \t\t\t\t\tpath: [],\n\t// \t\t\t\t\tfield: is(field, Column) ? aliasedTableColumn(field, tableAlias) : field,\n\t// \t\t\t\t})),\n\t// \t\t\t\tjoins,\n\t// \t\t\t\tdistinct,\n\t// \t\t\t}),\n\t// \t\t\t{},\n\t// \t\t\ttableAlias,\n\t// \t\t);\n\t// \t\tselectionForBuild = selection.map((item) =>\n\t// \t\t\tis(item.field, SQL.Aliased)\n\t// \t\t\t\t? { ...item, field: sql`${sql.identifier(tableAlias)}.${sql.identifier(item.field.fieldAlias)}` }\n\t// \t\t\t\t: item\n\t// \t\t);\n\t// \t\tjoins = [];\n\t// \t\tdistinct = undefined;\n\t// \t}\n\n\t// \tconst result = this.buildSelectQuery({\n\t// \t\ttable: is(tableFrom, PgTable) ? aliasedTable(tableFrom, tableAlias) : tableFrom,\n\t// \t\tfields: {},\n\t// \t\tfieldsFlat: selectionForBuild.map(({ field }) => ({\n\t// \t\t\tpath: [],\n\t// \t\t\tfield: is(field, Column) ? aliasedTableColumn(field, tableAlias) : field,\n\t// \t\t})),\n\t// \t\twhere,\n\t// \t\tlimit,\n\t// \t\toffset,\n\t// \t\tjoins,\n\t// \t\torderBy,\n\t// \t\tdistinct,\n\t// \t});\n\n\t// \treturn {\n\t// \t\ttableTsKey: tableConfig.tsName,\n\t// \t\tsql: result,\n\t// \t\tselection,\n\t// \t};\n\t// }\n\n\tbuildRelationalQueryWithoutPK({\n\t\tfullSchema,\n\t\tschema,\n\t\ttableNamesMap,\n\t\ttable,\n\t\ttableConfig,\n\t\tqueryConfig: config,\n\t\ttableAlias,\n\t\tnestedQueryRelation,\n\t\tjoinOn,\n\t}: {\n\t\tfullSchema: Record<string, unknown>;\n\t\tschema: TablesRelationalConfig;\n\t\ttableNamesMap: Record<string, string>;\n\t\ttable: PgTable;\n\t\ttableConfig: TableRelationalConfig;\n\t\tqueryConfig: true | DBQueryConfig<'many', true>;\n\t\ttableAlias: string;\n\t\tnestedQueryRelation?: Relation;\n\t\tjoinOn?: SQL;\n\t}): BuildRelationalQueryResult<PgTable, PgColumn> {\n\t\tlet selection: BuildRelationalQueryResult<PgTable, PgColumn>['selection'] = [];\n\t\tlet limit, offset, orderBy: NonNullable<PgSelectConfig['orderBy']> = [], where;\n\t\tconst joins: PgSelectJoinConfig[] = [];\n\n\t\tif (config === true) {\n\t\t\tconst selectionEntries = Object.entries(tableConfig.columns);\n\t\t\tselection = selectionEntries.map((\n\t\t\t\t[key, value],\n\t\t\t) => ({\n\t\t\t\tdbKey: value.name,\n\t\t\t\ttsKey: key,\n\t\t\t\tfield: aliasedTableColumn(value as PgColumn, tableAlias),\n\t\t\t\trelationTableTsKey: undefined,\n\t\t\t\tisJson: false,\n\t\t\t\tselection: [],\n\t\t\t}));\n\t\t} else {\n\t\t\tconst aliasedColumns = Object.fromEntries(\n\t\t\t\tObject.entries(tableConfig.columns).map((\n\t\t\t\t\t[key, value],\n\t\t\t\t) => [key, aliasedTableColumn(value, tableAlias)]),\n\t\t\t);\n\n\t\t\tif (config.where) {\n\t\t\t\tconst whereSql = typeof config.where === 'function'\n\t\t\t\t\t? config.where(aliasedColumns, getOperators())\n\t\t\t\t\t: config.where;\n\t\t\t\twhere = whereSql && mapColumnsInSQLToAlias(whereSql, tableAlias);\n\t\t\t}\n\n\t\t\tconst fieldsSelection: { tsKey: string; value: PgColumn | SQL.Aliased }[] = [];\n\t\t\tlet selectedColumns: string[] = [];\n\n\t\t\t// Figure out which columns to select\n\t\t\tif (config.columns) {\n\t\t\t\tlet isIncludeMode = false;\n\n\t\t\t\tfor (const [field, value] of Object.entries(config.columns)) {\n\t\t\t\t\tif (value === undefined) {\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\n\t\t\t\t\tif (field in tableConfig.columns) {\n\t\t\t\t\t\tif (!isIncludeMode && value === true) {\n\t\t\t\t\t\t\tisIncludeMode = true;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tselectedColumns.push(field);\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (selectedColumns.length > 0) {\n\t\t\t\t\tselectedColumns = isIncludeMode\n\t\t\t\t\t\t? selectedColumns.filter((c) => config.columns?.[c] === true)\n\t\t\t\t\t\t: Object.keys(tableConfig.columns).filter((key) => !selectedColumns.includes(key));\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\t// Select all columns if selection is not specified\n\t\t\t\tselectedColumns = Object.keys(tableConfig.columns);\n\t\t\t}\n\n\t\t\tfor (const field of selectedColumns) {\n\t\t\t\tconst column = tableConfig.columns[field]! as PgColumn;\n\t\t\t\tfieldsSelection.push({ tsKey: field, value: column });\n\t\t\t}\n\n\t\t\tlet selectedRelations: {\n\t\t\t\ttsKey: string;\n\t\t\t\tqueryConfig: true | DBQueryConfig<'many', false>;\n\t\t\t\trelation: Relation;\n\t\t\t}[] = [];\n\n\t\t\t// Figure out which relations to select\n\t\t\tif (config.with) {\n\t\t\t\tselectedRelations = Object.entries(config.with)\n\t\t\t\t\t.filter((entry): entry is [typeof entry[0], NonNullable<typeof entry[1]>] => !!entry[1])\n\t\t\t\t\t.map(([tsKey, queryConfig]) => ({ tsKey, queryConfig, relation: tableConfig.relations[tsKey]! }));\n\t\t\t}\n\n\t\t\tlet extras;\n\n\t\t\t// Figure out which extras to select\n\t\t\tif (config.extras) {\n\t\t\t\textras = typeof config.extras === 'function'\n\t\t\t\t\t? config.extras(aliasedColumns, { sql })\n\t\t\t\t\t: config.extras;\n\t\t\t\tfor (const [tsKey, value] of Object.entries(extras)) {\n\t\t\t\t\tfieldsSelection.push({\n\t\t\t\t\t\ttsKey,\n\t\t\t\t\t\tvalue: mapColumnsInAliasedSQLToAlias(value, tableAlias),\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Transform `fieldsSelection` into `selection`\n\t\t\t// `fieldsSelection` shouldn't be used after this point\n\t\t\tfor (const { tsKey, value } of fieldsSelection) {\n\t\t\t\tselection.push({\n\t\t\t\t\tdbKey: is(value, SQL.Aliased) ? value.fieldAlias : tableConfig.columns[tsKey]!.name,\n\t\t\t\t\ttsKey,\n\t\t\t\t\tfield: is(value, Column) ? aliasedTableColumn(value, tableAlias) : value,\n\t\t\t\t\trelationTableTsKey: undefined,\n\t\t\t\t\tisJson: false,\n\t\t\t\t\tselection: [],\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tlet orderByOrig = typeof config.orderBy === 'function'\n\t\t\t\t? config.orderBy(aliasedColumns, getOrderByOperators())\n\t\t\t\t: config.orderBy ?? [];\n\t\t\tif (!Array.isArray(orderByOrig)) {\n\t\t\t\torderByOrig = [orderByOrig];\n\t\t\t}\n\t\t\torderBy = orderByOrig.map((orderByValue) => {\n\t\t\t\tif (is(orderByValue, Column)) {\n\t\t\t\t\treturn aliasedTableColumn(orderByValue, tableAlias) as PgColumn;\n\t\t\t\t}\n\t\t\t\treturn mapColumnsInSQLToAlias(orderByValue, tableAlias);\n\t\t\t});\n\n\t\t\tlimit = config.limit;\n\t\t\toffset = config.offset;\n\n\t\t\t// Process all relations\n\t\t\tfor (\n\t\t\t\tconst {\n\t\t\t\t\ttsKey: selectedRelationTsKey,\n\t\t\t\t\tqueryConfig: selectedRelationConfigValue,\n\t\t\t\t\trelation,\n\t\t\t\t} of selectedRelations\n\t\t\t) {\n\t\t\t\tconst normalizedRelation = normalizeRelation(schema, tableNamesMap, relation);\n\t\t\t\tconst relationTableName = getTableUniqueName(relation.referencedTable);\n\t\t\t\tconst relationTableTsName = tableNamesMap[relationTableName]!;\n\t\t\t\tconst relationTableAlias = `${tableAlias}_${selectedRelationTsKey}`;\n\t\t\t\tconst joinOn = and(\n\t\t\t\t\t...normalizedRelation.fields.map((field, i) =>\n\t\t\t\t\t\teq(\n\t\t\t\t\t\t\taliasedTableColumn(normalizedRelation.references[i]!, relationTableAlias),\n\t\t\t\t\t\t\taliasedTableColumn(field, tableAlias),\n\t\t\t\t\t\t)\n\t\t\t\t\t),\n\t\t\t\t);\n\t\t\t\tconst builtRelation = this.buildRelationalQueryWithoutPK({\n\t\t\t\t\tfullSchema,\n\t\t\t\t\tschema,\n\t\t\t\t\ttableNamesMap,\n\t\t\t\t\ttable: fullSchema[relationTableTsName] as PgTable,\n\t\t\t\t\ttableConfig: schema[relationTableTsName]!,\n\t\t\t\t\tqueryConfig: is(relation, One)\n\t\t\t\t\t\t? (selectedRelationConfigValue === true\n\t\t\t\t\t\t\t? { limit: 1 }\n\t\t\t\t\t\t\t: { ...selectedRelationConfigValue, limit: 1 })\n\t\t\t\t\t\t: selectedRelationConfigValue,\n\t\t\t\t\ttableAlias: relationTableAlias,\n\t\t\t\t\tjoinOn,\n\t\t\t\t\tnestedQueryRelation: relation,\n\t\t\t\t});\n\t\t\t\tconst field = sql`${sql.identifier(relationTableAlias)}.${sql.identifier('data')}`.as(selectedRelationTsKey);\n\t\t\t\tjoins.push({\n\t\t\t\t\ton: sql`true`,\n\t\t\t\t\ttable: new Subquery(builtRelation.sql as SQL, {}, relationTableAlias),\n\t\t\t\t\talias: relationTableAlias,\n\t\t\t\t\tjoinType: 'left',\n\t\t\t\t\tlateral: true,\n\t\t\t\t});\n\t\t\t\tselection.push({\n\t\t\t\t\tdbKey: selectedRelationTsKey,\n\t\t\t\t\ttsKey: selectedRelationTsKey,\n\t\t\t\t\tfield,\n\t\t\t\t\trelationTableTsKey: relationTableTsName,\n\t\t\t\t\tisJson: true,\n\t\t\t\t\tselection: builtRelation.selection,\n\t\t\t\t});\n\t\t\t}\n\t\t}\n\n\t\tif (selection.length === 0) {\n\t\t\tthrow new DrizzleError({ message: `No fields selected for table \"${tableConfig.tsName}\" (\"${tableAlias}\")` });\n\t\t}\n\n\t\tlet result;\n\n\t\twhere = and(joinOn, where);\n\n\t\tif (nestedQueryRelation) {\n\t\t\tlet field = sql`json_build_array(${\n\t\t\t\tsql.join(\n\t\t\t\t\tselection.map(({ field, tsKey, isJson }) =>\n\t\t\t\t\t\tisJson\n\t\t\t\t\t\t\t? sql`${sql.identifier(`${tableAlias}_${tsKey}`)}.${sql.identifier('data')}`\n\t\t\t\t\t\t\t: is(field, SQL.Aliased)\n\t\t\t\t\t\t\t? field.sql\n\t\t\t\t\t\t\t: field\n\t\t\t\t\t),\n\t\t\t\t\tsql`, `,\n\t\t\t\t)\n\t\t\t})`;\n\t\t\tif (is(nestedQueryRelation, Many)) {\n\t\t\t\tfield = sql`coalesce(json_agg(${field}${\n\t\t\t\t\torderBy.length > 0 ? sql` order by ${sql.join(orderBy, sql`, `)}` : undefined\n\t\t\t\t}), '[]'::json)`;\n\t\t\t\t// orderBy = [];\n\t\t\t}\n\t\t\tconst nestedSelection = [{\n\t\t\t\tdbKey: 'data',\n\t\t\t\ttsKey: 'data',\n\t\t\t\tfield: field.as('data'),\n\t\t\t\tisJson: true,\n\t\t\t\trelationTableTsKey: tableConfig.tsName,\n\t\t\t\tselection,\n\t\t\t}];\n\n\t\t\tconst needsSubquery = limit !== undefined || offset !== undefined || orderBy.length > 0;\n\n\t\t\tif (needsSubquery) {\n\t\t\t\tresult = this.buildSelectQuery({\n\t\t\t\t\ttable: aliasedTable(table, tableAlias),\n\t\t\t\t\tfields: {},\n\t\t\t\t\tfieldsFlat: [{\n\t\t\t\t\t\tpath: [],\n\t\t\t\t\t\tfield: sql.raw('*'),\n\t\t\t\t\t}],\n\t\t\t\t\twhere,\n\t\t\t\t\tlimit,\n\t\t\t\t\toffset,\n\t\t\t\t\torderBy,\n\t\t\t\t\tsetOperators: [],\n\t\t\t\t});\n\n\t\t\t\twhere = undefined;\n\t\t\t\tlimit = undefined;\n\t\t\t\toffset = undefined;\n\t\t\t\torderBy = [];\n\t\t\t} else {\n\t\t\t\tresult = aliasedTable(table, tableAlias);\n\t\t\t}\n\n\t\t\tresult = this.buildSelectQuery({\n\t\t\t\ttable: is(result, PgTable) ? result : new Subquery(result, {}, tableAlias),\n\t\t\t\tfields: {},\n\t\t\t\tfieldsFlat: nestedSelection.map(({ field }) => ({\n\t\t\t\t\tpath: [],\n\t\t\t\t\tfield: is(field, Column) ? aliasedTableColumn(field, tableAlias) : field,\n\t\t\t\t})),\n\t\t\t\tjoins,\n\t\t\t\twhere,\n\t\t\t\tlimit,\n\t\t\t\toffset,\n\t\t\t\torderBy,\n\t\t\t\tsetOperators: [],\n\t\t\t});\n\t\t} else {\n\t\t\tresult = this.buildSelectQuery({\n\t\t\t\ttable: aliasedTable(table, tableAlias),\n\t\t\t\tfields: {},\n\t\t\t\tfieldsFlat: selection.map(({ field }) => ({\n\t\t\t\t\tpath: [],\n\t\t\t\t\tfield: is(field, Column) ? aliasedTableColumn(field, tableAlias) : field,\n\t\t\t\t})),\n\t\t\t\tjoins,\n\t\t\t\twhere,\n\t\t\t\tlimit,\n\t\t\t\toffset,\n\t\t\t\torderBy,\n\t\t\t\tsetOperators: [],\n\t\t\t});\n\t\t}\n\n\t\treturn {\n\t\t\ttableTsKey: tableConfig.tsName,\n\t\t\tsql: result,\n\t\t\tselection,\n\t\t};\n\t}\n}\n", "import { entityKind } from '~/entity.ts';\nimport type { SQL, SQLWrapper } from '~/sql/index.ts';\n\nexport abstract class TypedQueryBuilder<TSelection, TResult = unknown> implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'TypedQueryBuilder';\n\n\tdeclare _: {\n\t\tselectedFields: TSelection;\n\t\tresult: TResult;\n\t};\n\n\t/** @internal */\n\tgetSelectedFields(): TSelection {\n\t\treturn this._.selectedFields;\n\t}\n\n\tabstract getSQL(): SQL;\n}\n", "import { entityKind, is } from '~/entity.ts';\nimport type { PgColumn } from '~/pg-core/columns/index.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport type { PgSession, PreparedQueryConfig } from '~/pg-core/session.ts';\nimport type { SubqueryWithSelection } from '~/pg-core/subquery.ts';\nimport type { PgTable } from '~/pg-core/table.ts';\nimport { PgViewBase } from '~/pg-core/view-base.ts';\nimport { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport type {\n\tBuildSubquerySelection,\n\tGetSelectTableName,\n\tGetSelectTableSelection,\n\tJoinNullability,\n\tJoinType,\n\tSelectMode,\n\tSelectResult,\n\tSetOperator,\n} from '~/query-builders/select.types.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport { SelectionProxyHandler } from '~/selection-proxy.ts';\nimport { SQL, View } from '~/sql/sql.ts';\nimport type { ColumnsSelection, Placeholder, Query, SQLWrapper } from '~/sql/sql.ts';\nimport { Subquery } from '~/subquery.ts';\nimport { Table } from '~/table.ts';\nimport { tracer } from '~/tracing.ts';\nimport {\n\tapplyMixins,\n\ttype DrizzleTypeError,\n\tgetTableColumns,\n\tgetTableLikeName,\n\thaveSameKeys,\n\ttype NeonAuthToken,\n\ttype ValueOrArray,\n} from '~/utils.ts';\nimport { orderSelectedFields } from '~/utils.ts';\nimport { ViewBaseConfig } from '~/view-common.ts';\nimport type {\n\tAnyPgSelect,\n\tCreatePgSelectFromBuilderMode,\n\tGetPgSetOperators,\n\tLockConfig,\n\tLockStrength,\n\tPgCreateSetOperatorFn,\n\tPgSelectConfig,\n\tPgSelectDynamic,\n\tPgSelectHKT,\n\tPgSelectHKTBase,\n\tPgSelectJoinFn,\n\tPgSelectPrepare,\n\tPgSelectWithout,\n\tPgSetOperatorExcludedMethods,\n\tPgSetOperatorWithResult,\n\tSelectedFields,\n\tSetOperatorRightSelect,\n\tTableLikeHasEmptySelection,\n} from './select.types.ts';\n\nexport class PgSelectBuilder<\n\tTSelection extends SelectedFields | undefined,\n\tTBuilderMode extends 'db' | 'qb' = 'db',\n> {\n\tstatic readonly [entityKind]: string = 'PgSelectBuilder';\n\n\tprivate fields: TSelection;\n\tprivate session: PgSession | undefined;\n\tprivate dialect: PgDialect;\n\tprivate withList: Subquery[] = [];\n\tprivate distinct: boolean | {\n\t\ton: (PgColumn | SQLWrapper)[];\n\t} | undefined;\n\n\tconstructor(\n\t\tconfig: {\n\t\t\tfields: TSelection;\n\t\t\tsession: PgSession | undefined;\n\t\t\tdialect: PgDialect;\n\t\t\twithList?: Subquery[];\n\t\t\tdistinct?: boolean | {\n\t\t\t\ton: (PgColumn | SQLWrapper)[];\n\t\t\t};\n\t\t},\n\t) {\n\t\tthis.fields = config.fields;\n\t\tthis.session = config.session;\n\t\tthis.dialect = config.dialect;\n\t\tif (config.withList) {\n\t\t\tthis.withList = config.withList;\n\t\t}\n\t\tthis.distinct = config.distinct;\n\t}\n\n\tprivate authToken?: NeonAuthToken;\n\t/** @internal */\n\tsetToken(token?: NeonAuthToken) {\n\t\tthis.authToken = token;\n\t\treturn this;\n\t}\n\n\t/**\n\t * Specify the table, subquery, or other target that you're\n\t * building a select query against.\n\t *\n\t * {@link https://www.postgresql.org/docs/current/sql-select.html#SQL-FROM | Postgres from documentation}\n\t */\n\tfrom<TFrom extends PgTable | Subquery | PgViewBase | SQL>(\n\t\tsource: TableLikeHasEmptySelection<TFrom> extends true ? DrizzleTypeError<\n\t\t\t\t\"Cannot reference a data-modifying statement subquery if it doesn't contain a `returning` clause\"\n\t\t\t>\n\t\t\t: TFrom,\n\t): CreatePgSelectFromBuilderMode<\n\t\tTBuilderMode,\n\t\tGetSelectTableName<TFrom>,\n\t\tTSelection extends undefined ? GetSelectTableSelection<TFrom> : TSelection,\n\t\tTSelection extends undefined ? 'single' : 'partial'\n\t> {\n\t\tconst isPartialSelect = !!this.fields;\n\t\tconst src = source as TFrom;\n\n\t\tlet fields: SelectedFields;\n\t\tif (this.fields) {\n\t\t\tfields = this.fields;\n\t\t} else if (is(src, Subquery)) {\n\t\t\t// This is required to use the proxy handler to get the correct field values from the subquery\n\t\t\tfields = Object.fromEntries(\n\t\t\t\tObject.keys(src._.selectedFields).map((\n\t\t\t\t\tkey,\n\t\t\t\t) => [key, src[key as unknown as keyof typeof src] as unknown as SelectedFields[string]]),\n\t\t\t);\n\t\t} else if (is(src, PgViewBase)) {\n\t\t\tfields = src[ViewBaseConfig].selectedFields as SelectedFields;\n\t\t} else if (is(src, SQL)) {\n\t\t\tfields = {};\n\t\t} else {\n\t\t\tfields = getTableColumns<PgTable>(src);\n\t\t}\n\n\t\treturn (new PgSelectBase({\n\t\t\ttable: src,\n\t\t\tfields,\n\t\t\tisPartialSelect,\n\t\t\tsession: this.session,\n\t\t\tdialect: this.dialect,\n\t\t\twithList: this.withList,\n\t\t\tdistinct: this.distinct,\n\t\t}).setToken(this.authToken)) as any;\n\t}\n}\n\nexport abstract class PgSelectQueryBuilderBase<\n\tTHKT extends PgSelectHKTBase,\n\tTTableName extends string | undefined,\n\tTSelection extends ColumnsSelection,\n\tTSelectMode extends SelectMode,\n\tTNullabilityMap extends Record<string, JoinNullability> = TTableName extends string ? Record<TTableName, 'not-null'>\n\t\t: {},\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n\tTResult extends any[] = SelectResult<TSelection, TSelectMode, TNullabilityMap>[],\n\tTSelectedFields extends ColumnsSelection = BuildSubquerySelection<TSelection, TNullabilityMap>,\n> extends TypedQueryBuilder<TSelectedFields, TResult> {\n\tstatic override readonly [entityKind]: string = 'PgSelectQueryBuilder';\n\n\toverride readonly _: {\n\t\treadonly dialect: 'pg';\n\t\treadonly hkt: THKT;\n\t\treadonly tableName: TTableName;\n\t\treadonly selection: TSelection;\n\t\treadonly selectMode: TSelectMode;\n\t\treadonly nullabilityMap: TNullabilityMap;\n\t\treadonly dynamic: TDynamic;\n\t\treadonly excludedMethods: TExcludedMethods;\n\t\treadonly result: TResult;\n\t\treadonly selectedFields: TSelectedFields;\n\t};\n\n\tprotected config: PgSelectConfig;\n\tprotected joinsNotNullableMap: Record<string, boolean>;\n\tprivate tableName: string | undefined;\n\tprivate isPartialSelect: boolean;\n\tprotected session: PgSession | undefined;\n\tprotected dialect: PgDialect;\n\n\tconstructor(\n\t\t{ table, fields, isPartialSelect, session, dialect, withList, distinct }: {\n\t\t\ttable: PgSelectConfig['table'];\n\t\t\tfields: PgSelectConfig['fields'];\n\t\t\tisPartialSelect: boolean;\n\t\t\tsession: PgSession | undefined;\n\t\t\tdialect: PgDialect;\n\t\t\twithList: Subquery[];\n\t\t\tdistinct: boolean | {\n\t\t\t\ton: (PgColumn | SQLWrapper)[];\n\t\t\t} | undefined;\n\t\t},\n\t) {\n\t\tsuper();\n\t\tthis.config = {\n\t\t\twithList,\n\t\t\ttable,\n\t\t\tfields: { ...fields },\n\t\t\tdistinct,\n\t\t\tsetOperators: [],\n\t\t};\n\t\tthis.isPartialSelect = isPartialSelect;\n\t\tthis.session = session;\n\t\tthis.dialect = dialect;\n\t\tthis._ = {\n\t\t\tselectedFields: fields as TSelectedFields,\n\t\t} as this['_'];\n\t\tthis.tableName = getTableLikeName(table);\n\t\tthis.joinsNotNullableMap = typeof this.tableName === 'string' ? { [this.tableName]: true } : {};\n\t}\n\n\tprivate createJoin<TJoinType extends JoinType>(\n\t\tjoinType: TJoinType,\n\t): PgSelectJoinFn<this, TDynamic, TJoinType> {\n\t\treturn ((\n\t\t\ttable: PgTable | Subquery | PgViewBase | SQL,\n\t\t\ton: ((aliases: TSelection) => SQL | undefined) | SQL | undefined,\n\t\t) => {\n\t\t\tconst baseTableName = this.tableName;\n\t\t\tconst tableName = getTableLikeName(table);\n\n\t\t\tif (typeof tableName === 'string' && this.config.joins?.some((join) => join.alias === tableName)) {\n\t\t\t\tthrow new Error(`Alias \"${tableName}\" is already used in this query`);\n\t\t\t}\n\n\t\t\tif (!this.isPartialSelect) {\n\t\t\t\t// If this is the first join and this is not a partial select and we're not selecting from raw SQL, \"move\" the fields from the main table to the nested object\n\t\t\t\tif (Object.keys(this.joinsNotNullableMap).length === 1 && typeof baseTableName === 'string') {\n\t\t\t\t\tthis.config.fields = {\n\t\t\t\t\t\t[baseTableName]: this.config.fields,\n\t\t\t\t\t};\n\t\t\t\t}\n\t\t\t\tif (typeof tableName === 'string' && !is(table, SQL)) {\n\t\t\t\t\tconst selection = is(table, Subquery)\n\t\t\t\t\t\t? table._.selectedFields\n\t\t\t\t\t\t: is(table, View)\n\t\t\t\t\t\t? table[ViewBaseConfig].selectedFields\n\t\t\t\t\t\t: table[Table.Symbol.Columns];\n\t\t\t\t\tthis.config.fields[tableName] = selection;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (typeof on === 'function') {\n\t\t\t\ton = on(\n\t\t\t\t\tnew Proxy(\n\t\t\t\t\t\tthis.config.fields,\n\t\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'sql', sqlBehavior: 'sql' }),\n\t\t\t\t\t) as TSelection,\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tif (!this.config.joins) {\n\t\t\t\tthis.config.joins = [];\n\t\t\t}\n\n\t\t\tthis.config.joins.push({ on, table, joinType, alias: tableName });\n\n\t\t\tif (typeof tableName === 'string') {\n\t\t\t\tswitch (joinType) {\n\t\t\t\t\tcase 'left': {\n\t\t\t\t\t\tthis.joinsNotNullableMap[tableName] = false;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'right': {\n\t\t\t\t\t\tthis.joinsNotNullableMap = Object.fromEntries(\n\t\t\t\t\t\t\tObject.entries(this.joinsNotNullableMap).map(([key]) => [key, false]),\n\t\t\t\t\t\t);\n\t\t\t\t\t\tthis.joinsNotNullableMap[tableName] = true;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'inner': {\n\t\t\t\t\t\tthis.joinsNotNullableMap[tableName] = true;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'full': {\n\t\t\t\t\t\tthis.joinsNotNullableMap = Object.fromEntries(\n\t\t\t\t\t\t\tObject.entries(this.joinsNotNullableMap).map(([key]) => [key, false]),\n\t\t\t\t\t\t);\n\t\t\t\t\t\tthis.joinsNotNullableMap[tableName] = false;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn this as any;\n\t\t}) as any;\n\t}\n\n\t/**\n\t * Executes a `left join` operation by adding another table to the current query.\n\t *\n\t * Calling this method associates each row of the table with the corresponding row from the joined table, if a match is found. If no matching row exists, it sets all columns of the joined table to null.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/joins#left-join}\n\t *\n\t * @param table the table to join.\n\t * @param on the `on` clause.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all users and their pets\n\t * const usersWithPets: { user: User; pets: Pet | null }[] = await db.select()\n\t *   .from(users)\n\t *   .leftJoin(pets, eq(users.id, pets.ownerId))\n\t *\n\t * // Select userId and petId\n\t * const usersIdsAndPetIds: { userId: number; petId: number | null }[] = await db.select({\n\t *   userId: users.id,\n\t *   petId: pets.id,\n\t * })\n\t *   .from(users)\n\t *   .leftJoin(pets, eq(users.id, pets.ownerId))\n\t * ```\n\t */\n\tleftJoin = this.createJoin('left');\n\n\t/**\n\t * Executes a `right join` operation by adding another table to the current query.\n\t *\n\t * Calling this method associates each row of the joined table with the corresponding row from the main table, if a match is found. If no matching row exists, it sets all columns of the main table to null.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/joins#right-join}\n\t *\n\t * @param table the table to join.\n\t * @param on the `on` clause.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all users and their pets\n\t * const usersWithPets: { user: User | null; pets: Pet }[] = await db.select()\n\t *   .from(users)\n\t *   .rightJoin(pets, eq(users.id, pets.ownerId))\n\t *\n\t * // Select userId and petId\n\t * const usersIdsAndPetIds: { userId: number | null; petId: number }[] = await db.select({\n\t *   userId: users.id,\n\t *   petId: pets.id,\n\t * })\n\t *   .from(users)\n\t *   .rightJoin(pets, eq(users.id, pets.ownerId))\n\t * ```\n\t */\n\trightJoin = this.createJoin('right');\n\n\t/**\n\t * Executes an `inner join` operation, creating a new table by combining rows from two tables that have matching values.\n\t *\n\t * Calling this method retrieves rows that have corresponding entries in both joined tables. Rows without matching entries in either table are excluded, resulting in a table that includes only matching pairs.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/joins#inner-join}\n\t *\n\t * @param table the table to join.\n\t * @param on the `on` clause.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all users and their pets\n\t * const usersWithPets: { user: User; pets: Pet }[] = await db.select()\n\t *   .from(users)\n\t *   .innerJoin(pets, eq(users.id, pets.ownerId))\n\t *\n\t * // Select userId and petId\n\t * const usersIdsAndPetIds: { userId: number; petId: number }[] = await db.select({\n\t *   userId: users.id,\n\t *   petId: pets.id,\n\t * })\n\t *   .from(users)\n\t *   .innerJoin(pets, eq(users.id, pets.ownerId))\n\t * ```\n\t */\n\tinnerJoin = this.createJoin('inner');\n\n\t/**\n\t * Executes a `full join` operation by combining rows from two tables into a new table.\n\t *\n\t * Calling this method retrieves all rows from both main and joined tables, merging rows with matching values and filling in `null` for non-matching columns.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/joins#full-join}\n\t *\n\t * @param table the table to join.\n\t * @param on the `on` clause.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all users and their pets\n\t * const usersWithPets: { user: User | null; pets: Pet | null }[] = await db.select()\n\t *   .from(users)\n\t *   .fullJoin(pets, eq(users.id, pets.ownerId))\n\t *\n\t * // Select userId and petId\n\t * const usersIdsAndPetIds: { userId: number | null; petId: number | null }[] = await db.select({\n\t *   userId: users.id,\n\t *   petId: pets.id,\n\t * })\n\t *   .from(users)\n\t *   .fullJoin(pets, eq(users.id, pets.ownerId))\n\t * ```\n\t */\n\tfullJoin = this.createJoin('full');\n\n\tprivate createSetOperator(\n\t\ttype: SetOperator,\n\t\tisAll: boolean,\n\t): <TValue extends PgSetOperatorWithResult<TResult>>(\n\t\trightSelection:\n\t\t\t| ((setOperators: GetPgSetOperators) => SetOperatorRightSelect<TValue, TResult>)\n\t\t\t| SetOperatorRightSelect<TValue, TResult>,\n\t) => PgSelectWithout<\n\t\tthis,\n\t\tTDynamic,\n\t\tPgSetOperatorExcludedMethods,\n\t\ttrue\n\t> {\n\t\treturn (rightSelection) => {\n\t\t\tconst rightSelect = (typeof rightSelection === 'function'\n\t\t\t\t? rightSelection(getPgSetOperators())\n\t\t\t\t: rightSelection) as TypedQueryBuilder<\n\t\t\t\t\tany,\n\t\t\t\t\tTResult\n\t\t\t\t>;\n\n\t\t\tif (!haveSameKeys(this.getSelectedFields(), rightSelect.getSelectedFields())) {\n\t\t\t\tthrow new Error(\n\t\t\t\t\t'Set operator error (union / intersect / except): selected fields are not the same or are in a different order',\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tthis.config.setOperators.push({ type, isAll, rightSelect });\n\t\t\treturn this as any;\n\t\t};\n\t}\n\n\t/**\n\t * Adds `union` set operator to the query.\n\t *\n\t * Calling this method will combine the result sets of the `select` statements and remove any duplicate rows that appear across them.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/set-operations#union}\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all unique names from customers and users tables\n\t * await db.select({ name: users.name })\n\t *   .from(users)\n\t *   .union(\n\t *     db.select({ name: customers.name }).from(customers)\n\t *   );\n\t * // or\n\t * import { union } from 'drizzle-orm/pg-core'\n\t *\n\t * await union(\n\t *   db.select({ name: users.name }).from(users),\n\t *   db.select({ name: customers.name }).from(customers)\n\t * );\n\t * ```\n\t */\n\tunion = this.createSetOperator('union', false);\n\n\t/**\n\t * Adds `union all` set operator to the query.\n\t *\n\t * Calling this method will combine the result-set of the `select` statements and keep all duplicate rows that appear across them.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/set-operations#union-all}\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all transaction ids from both online and in-store sales\n\t * await db.select({ transaction: onlineSales.transactionId })\n\t *   .from(onlineSales)\n\t *   .unionAll(\n\t *     db.select({ transaction: inStoreSales.transactionId }).from(inStoreSales)\n\t *   );\n\t * // or\n\t * import { unionAll } from 'drizzle-orm/pg-core'\n\t *\n\t * await unionAll(\n\t *   db.select({ transaction: onlineSales.transactionId }).from(onlineSales),\n\t *   db.select({ transaction: inStoreSales.transactionId }).from(inStoreSales)\n\t * );\n\t * ```\n\t */\n\tunionAll = this.createSetOperator('union', true);\n\n\t/**\n\t * Adds `intersect` set operator to the query.\n\t *\n\t * Calling this method will retain only the rows that are present in both result sets and eliminate duplicates.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/set-operations#intersect}\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select course names that are offered in both departments A and B\n\t * await db.select({ courseName: depA.courseName })\n\t *   .from(depA)\n\t *   .intersect(\n\t *     db.select({ courseName: depB.courseName }).from(depB)\n\t *   );\n\t * // or\n\t * import { intersect } from 'drizzle-orm/pg-core'\n\t *\n\t * await intersect(\n\t *   db.select({ courseName: depA.courseName }).from(depA),\n\t *   db.select({ courseName: depB.courseName }).from(depB)\n\t * );\n\t * ```\n\t */\n\tintersect = this.createSetOperator('intersect', false);\n\n\t/**\n\t * Adds `intersect all` set operator to the query.\n\t *\n\t * Calling this method will retain only the rows that are present in both result sets including all duplicates.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/set-operations#intersect-all}\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all products and quantities that are ordered by both regular and VIP customers\n\t * await db.select({\n\t *   productId: regularCustomerOrders.productId,\n\t *   quantityOrdered: regularCustomerOrders.quantityOrdered\n\t * })\n\t * .from(regularCustomerOrders)\n\t * .intersectAll(\n\t *   db.select({\n\t *     productId: vipCustomerOrders.productId,\n\t *     quantityOrdered: vipCustomerOrders.quantityOrdered\n\t *   })\n\t *   .from(vipCustomerOrders)\n\t * );\n\t * // or\n\t * import { intersectAll } from 'drizzle-orm/pg-core'\n\t *\n\t * await intersectAll(\n\t *   db.select({\n\t *     productId: regularCustomerOrders.productId,\n\t *     quantityOrdered: regularCustomerOrders.quantityOrdered\n\t *   })\n\t *   .from(regularCustomerOrders),\n\t *   db.select({\n\t *     productId: vipCustomerOrders.productId,\n\t *     quantityOrdered: vipCustomerOrders.quantityOrdered\n\t *   })\n\t *   .from(vipCustomerOrders)\n\t * );\n\t * ```\n\t */\n\tintersectAll = this.createSetOperator('intersect', true);\n\n\t/**\n\t * Adds `except` set operator to the query.\n\t *\n\t * Calling this method will retrieve all unique rows from the left query, except for the rows that are present in the result set of the right query.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/set-operations#except}\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all courses offered in department A but not in department B\n\t * await db.select({ courseName: depA.courseName })\n\t *   .from(depA)\n\t *   .except(\n\t *     db.select({ courseName: depB.courseName }).from(depB)\n\t *   );\n\t * // or\n\t * import { except } from 'drizzle-orm/pg-core'\n\t *\n\t * await except(\n\t *   db.select({ courseName: depA.courseName }).from(depA),\n\t *   db.select({ courseName: depB.courseName }).from(depB)\n\t * );\n\t * ```\n\t */\n\texcept = this.createSetOperator('except', false);\n\n\t/**\n\t * Adds `except all` set operator to the query.\n\t *\n\t * Calling this method will retrieve all rows from the left query, except for the rows that are present in the result set of the right query.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/set-operations#except-all}\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all products that are ordered by regular customers but not by VIP customers\n\t * await db.select({\n\t *   productId: regularCustomerOrders.productId,\n\t *   quantityOrdered: regularCustomerOrders.quantityOrdered,\n\t * })\n\t * .from(regularCustomerOrders)\n\t * .exceptAll(\n\t *   db.select({\n\t *     productId: vipCustomerOrders.productId,\n\t *     quantityOrdered: vipCustomerOrders.quantityOrdered,\n\t *   })\n\t *   .from(vipCustomerOrders)\n\t * );\n\t * // or\n\t * import { exceptAll } from 'drizzle-orm/pg-core'\n\t *\n\t * await exceptAll(\n\t *   db.select({\n\t *     productId: regularCustomerOrders.productId,\n\t *     quantityOrdered: regularCustomerOrders.quantityOrdered\n\t *   })\n\t *   .from(regularCustomerOrders),\n\t *   db.select({\n\t *     productId: vipCustomerOrders.productId,\n\t *     quantityOrdered: vipCustomerOrders.quantityOrdered\n\t *   })\n\t *   .from(vipCustomerOrders)\n\t * );\n\t * ```\n\t */\n\texceptAll = this.createSetOperator('except', true);\n\n\t/** @internal */\n\taddSetOperators(setOperators: PgSelectConfig['setOperators']): PgSelectWithout<\n\t\tthis,\n\t\tTDynamic,\n\t\tPgSetOperatorExcludedMethods,\n\t\ttrue\n\t> {\n\t\tthis.config.setOperators.push(...setOperators);\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds a `where` clause to the query.\n\t *\n\t * Calling this method will select only those rows that fulfill a specified condition.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#filtering}\n\t *\n\t * @param where the `where` clause.\n\t *\n\t * @example\n\t * You can use conditional operators and `sql function` to filter the rows to be selected.\n\t *\n\t * ```ts\n\t * // Select all cars with green color\n\t * await db.select().from(cars).where(eq(cars.color, 'green'));\n\t * // or\n\t * await db.select().from(cars).where(sql`${cars.color} = 'green'`)\n\t * ```\n\t *\n\t * You can logically combine conditional operators with `and()` and `or()` operators:\n\t *\n\t * ```ts\n\t * // Select all BMW cars with a green color\n\t * await db.select().from(cars).where(and(eq(cars.color, 'green'), eq(cars.brand, 'BMW')));\n\t *\n\t * // Select all cars with the green or blue color\n\t * await db.select().from(cars).where(or(eq(cars.color, 'green'), eq(cars.color, 'blue')));\n\t * ```\n\t */\n\twhere(\n\t\twhere: ((aliases: this['_']['selection']) => SQL | undefined) | SQL | undefined,\n\t): PgSelectWithout<this, TDynamic, 'where'> {\n\t\tif (typeof where === 'function') {\n\t\t\twhere = where(\n\t\t\t\tnew Proxy(\n\t\t\t\t\tthis.config.fields,\n\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'sql', sqlBehavior: 'sql' }),\n\t\t\t\t) as TSelection,\n\t\t\t);\n\t\t}\n\t\tthis.config.where = where;\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds a `having` clause to the query.\n\t *\n\t * Calling this method will select only those rows that fulfill a specified condition. It is typically used with aggregate functions to filter the aggregated data based on a specified condition.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#aggregations}\n\t *\n\t * @param having the `having` clause.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all brands with more than one car\n\t * await db.select({\n\t * \tbrand: cars.brand,\n\t * \tcount: sql<number>`cast(count(${cars.id}) as int)`,\n\t * })\n\t *   .from(cars)\n\t *   .groupBy(cars.brand)\n\t *   .having(({ count }) => gt(count, 1));\n\t * ```\n\t */\n\thaving(\n\t\thaving: ((aliases: this['_']['selection']) => SQL | undefined) | SQL | undefined,\n\t): PgSelectWithout<this, TDynamic, 'having'> {\n\t\tif (typeof having === 'function') {\n\t\t\thaving = having(\n\t\t\t\tnew Proxy(\n\t\t\t\t\tthis.config.fields,\n\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'sql', sqlBehavior: 'sql' }),\n\t\t\t\t) as TSelection,\n\t\t\t);\n\t\t}\n\t\tthis.config.having = having;\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds a `group by` clause to the query.\n\t *\n\t * Calling this method will group rows that have the same values into summary rows, often used for aggregation purposes.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#aggregations}\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Group and count people by their last names\n\t * await db.select({\n\t *    lastName: people.lastName,\n\t *    count: sql<number>`cast(count(*) as int)`\n\t * })\n\t *   .from(people)\n\t *   .groupBy(people.lastName);\n\t * ```\n\t */\n\tgroupBy(\n\t\tbuilder: (aliases: this['_']['selection']) => ValueOrArray<PgColumn | SQL | SQL.Aliased>,\n\t): PgSelectWithout<this, TDynamic, 'groupBy'>;\n\tgroupBy(...columns: (PgColumn | SQL | SQL.Aliased)[]): PgSelectWithout<this, TDynamic, 'groupBy'>;\n\tgroupBy(\n\t\t...columns:\n\t\t\t| [(aliases: this['_']['selection']) => ValueOrArray<PgColumn | SQL | SQL.Aliased>]\n\t\t\t| (PgColumn | SQL | SQL.Aliased)[]\n\t): PgSelectWithout<this, TDynamic, 'groupBy'> {\n\t\tif (typeof columns[0] === 'function') {\n\t\t\tconst groupBy = columns[0](\n\t\t\t\tnew Proxy(\n\t\t\t\t\tthis.config.fields,\n\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'alias', sqlBehavior: 'sql' }),\n\t\t\t\t) as TSelection,\n\t\t\t);\n\t\t\tthis.config.groupBy = Array.isArray(groupBy) ? groupBy : [groupBy];\n\t\t} else {\n\t\t\tthis.config.groupBy = columns as (PgColumn | SQL | SQL.Aliased)[];\n\t\t}\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds an `order by` clause to the query.\n\t *\n\t * Calling this method will sort the result-set in ascending or descending order. By default, the sort order is ascending.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#order-by}\n\t *\n\t * @example\n\t *\n\t * ```\n\t * // Select cars ordered by year\n\t * await db.select().from(cars).orderBy(cars.year);\n\t * ```\n\t *\n\t * You can specify whether results are in ascending or descending order with the `asc()` and `desc()` operators.\n\t *\n\t * ```ts\n\t * // Select cars ordered by year in descending order\n\t * await db.select().from(cars).orderBy(desc(cars.year));\n\t *\n\t * // Select cars ordered by year and price\n\t * await db.select().from(cars).orderBy(asc(cars.year), desc(cars.price));\n\t * ```\n\t */\n\torderBy(\n\t\tbuilder: (aliases: this['_']['selection']) => ValueOrArray<PgColumn | SQL | SQL.Aliased>,\n\t): PgSelectWithout<this, TDynamic, 'orderBy'>;\n\torderBy(...columns: (PgColumn | SQL | SQL.Aliased)[]): PgSelectWithout<this, TDynamic, 'orderBy'>;\n\torderBy(\n\t\t...columns:\n\t\t\t| [(aliases: this['_']['selection']) => ValueOrArray<PgColumn | SQL | SQL.Aliased>]\n\t\t\t| (PgColumn | SQL | SQL.Aliased)[]\n\t): PgSelectWithout<this, TDynamic, 'orderBy'> {\n\t\tif (typeof columns[0] === 'function') {\n\t\t\tconst orderBy = columns[0](\n\t\t\t\tnew Proxy(\n\t\t\t\t\tthis.config.fields,\n\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'alias', sqlBehavior: 'sql' }),\n\t\t\t\t) as TSelection,\n\t\t\t);\n\n\t\t\tconst orderByArray = Array.isArray(orderBy) ? orderBy : [orderBy];\n\n\t\t\tif (this.config.setOperators.length > 0) {\n\t\t\t\tthis.config.setOperators.at(-1)!.orderBy = orderByArray;\n\t\t\t} else {\n\t\t\t\tthis.config.orderBy = orderByArray;\n\t\t\t}\n\t\t} else {\n\t\t\tconst orderByArray = columns as (PgColumn | SQL | SQL.Aliased)[];\n\n\t\t\tif (this.config.setOperators.length > 0) {\n\t\t\t\tthis.config.setOperators.at(-1)!.orderBy = orderByArray;\n\t\t\t} else {\n\t\t\t\tthis.config.orderBy = orderByArray;\n\t\t\t}\n\t\t}\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds a `limit` clause to the query.\n\t *\n\t * Calling this method will set the maximum number of rows that will be returned by this query.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#limit--offset}\n\t *\n\t * @param limit the `limit` clause.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Get the first 10 people from this query.\n\t * await db.select().from(people).limit(10);\n\t * ```\n\t */\n\tlimit(limit: number | Placeholder): PgSelectWithout<this, TDynamic, 'limit'> {\n\t\tif (this.config.setOperators.length > 0) {\n\t\t\tthis.config.setOperators.at(-1)!.limit = limit;\n\t\t} else {\n\t\t\tthis.config.limit = limit;\n\t\t}\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds an `offset` clause to the query.\n\t *\n\t * Calling this method will skip a number of rows when returning results from this query.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#limit--offset}\n\t *\n\t * @param offset the `offset` clause.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Get the 10th-20th people from this query.\n\t * await db.select().from(people).offset(10).limit(10);\n\t * ```\n\t */\n\toffset(offset: number | Placeholder): PgSelectWithout<this, TDynamic, 'offset'> {\n\t\tif (this.config.setOperators.length > 0) {\n\t\t\tthis.config.setOperators.at(-1)!.offset = offset;\n\t\t} else {\n\t\t\tthis.config.offset = offset;\n\t\t}\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds a `for` clause to the query.\n\t *\n\t * Calling this method will specify a lock strength for this query that controls how strictly it acquires exclusive access to the rows being queried.\n\t *\n\t * See docs: {@link https://www.postgresql.org/docs/current/sql-select.html#SQL-FOR-UPDATE-SHARE}\n\t *\n\t * @param strength the lock strength.\n\t * @param config the lock configuration.\n\t */\n\tfor(strength: LockStrength, config: LockConfig = {}): PgSelectWithout<this, TDynamic, 'for'> {\n\t\tthis.config.lockingClause = { strength, config };\n\t\treturn this as any;\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this.dialect.buildSelectQuery(this.config);\n\t}\n\n\ttoSQL(): Query {\n\t\tconst { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n\t\treturn rest;\n\t}\n\n\tas<TAlias extends string>(\n\t\talias: TAlias,\n\t): SubqueryWithSelection<this['_']['selectedFields'], TAlias> {\n\t\treturn new Proxy(\n\t\t\tnew Subquery(this.getSQL(), this.config.fields, alias),\n\t\t\tnew SelectionProxyHandler({ alias, sqlAliasedBehavior: 'alias', sqlBehavior: 'error' }),\n\t\t) as SubqueryWithSelection<this['_']['selectedFields'], TAlias>;\n\t}\n\n\t/** @internal */\n\toverride getSelectedFields(): this['_']['selectedFields'] {\n\t\treturn new Proxy(\n\t\t\tthis.config.fields,\n\t\t\tnew SelectionProxyHandler({ alias: this.tableName, sqlAliasedBehavior: 'alias', sqlBehavior: 'error' }),\n\t\t) as this['_']['selectedFields'];\n\t}\n\n\t$dynamic(): PgSelectDynamic<this> {\n\t\treturn this;\n\t}\n}\n\nexport interface PgSelectBase<\n\tTTableName extends string | undefined,\n\tTSelection extends ColumnsSelection,\n\tTSelectMode extends SelectMode,\n\tTNullabilityMap extends Record<string, JoinNullability> = TTableName extends string ? Record<TTableName, 'not-null'>\n\t\t: {},\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n\tTResult extends any[] = SelectResult<TSelection, TSelectMode, TNullabilityMap>[],\n\tTSelectedFields extends ColumnsSelection = BuildSubquerySelection<TSelection, TNullabilityMap>,\n> extends\n\tPgSelectQueryBuilderBase<\n\t\tPgSelectHKT,\n\t\tTTableName,\n\t\tTSelection,\n\t\tTSelectMode,\n\t\tTNullabilityMap,\n\t\tTDynamic,\n\t\tTExcludedMethods,\n\t\tTResult,\n\t\tTSelectedFields\n\t>,\n\tQueryPromise<TResult>,\n\tSQLWrapper\n{}\n\nexport class PgSelectBase<\n\tTTableName extends string | undefined,\n\tTSelection extends ColumnsSelection,\n\tTSelectMode extends SelectMode,\n\tTNullabilityMap extends Record<string, JoinNullability> = TTableName extends string ? Record<TTableName, 'not-null'>\n\t\t: {},\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n\tTResult = SelectResult<TSelection, TSelectMode, TNullabilityMap>[],\n\tTSelectedFields = BuildSubquerySelection<TSelection, TNullabilityMap>,\n> extends PgSelectQueryBuilderBase<\n\tPgSelectHKT,\n\tTTableName,\n\tTSelection,\n\tTSelectMode,\n\tTNullabilityMap,\n\tTDynamic,\n\tTExcludedMethods,\n\tTResult,\n\tTSelectedFields\n> implements RunnableQuery<TResult, 'pg'>, SQLWrapper {\n\tstatic override readonly [entityKind]: string = 'PgSelect';\n\n\t/** @internal */\n\t_prepare(name?: string): PgSelectPrepare<this> {\n\t\tconst { session, config, dialect, joinsNotNullableMap, authToken } = this;\n\t\tif (!session) {\n\t\t\tthrow new Error('Cannot execute a query on a query builder. Please use a database instance instead.');\n\t\t}\n\t\treturn tracer.startActiveSpan('drizzle.prepareQuery', () => {\n\t\t\tconst fieldsList = orderSelectedFields<PgColumn>(config.fields);\n\t\t\tconst query = session.prepareQuery<\n\t\t\t\tPreparedQueryConfig & { execute: TResult }\n\t\t\t>(dialect.sqlToQuery(this.getSQL()), fieldsList, name, true);\n\t\t\tquery.joinsNotNullableMap = joinsNotNullableMap;\n\n\t\t\treturn query.setToken(authToken);\n\t\t});\n\t}\n\n\t/**\n\t * Create a prepared statement for this query. This allows\n\t * the database to remember this query for the given session\n\t * and call it by name, rather than specifying the full query.\n\t *\n\t * {@link https://www.postgresql.org/docs/current/sql-prepare.html | Postgres prepare documentation}\n\t */\n\tprepare(name: string): PgSelectPrepare<this> {\n\t\treturn this._prepare(name);\n\t}\n\n\tprivate authToken?: NeonAuthToken;\n\t/** @internal */\n\tsetToken(token?: NeonAuthToken) {\n\t\tthis.authToken = token;\n\t\treturn this;\n\t}\n\n\texecute: ReturnType<this['prepare']>['execute'] = (placeholderValues) => {\n\t\treturn tracer.startActiveSpan('drizzle.operation', () => {\n\t\t\treturn this._prepare().execute(placeholderValues, this.authToken);\n\t\t});\n\t};\n}\n\napplyMixins(PgSelectBase, [QueryPromise]);\n\nfunction createSetOperator(type: SetOperator, isAll: boolean): PgCreateSetOperatorFn {\n\treturn (leftSelect, rightSelect, ...restSelects) => {\n\t\tconst setOperators = [rightSelect, ...restSelects].map((select) => ({\n\t\t\ttype,\n\t\t\tisAll,\n\t\t\trightSelect: select as AnyPgSelect,\n\t\t}));\n\n\t\tfor (const setOperator of setOperators) {\n\t\t\tif (!haveSameKeys((leftSelect as any).getSelectedFields(), setOperator.rightSelect.getSelectedFields())) {\n\t\t\t\tthrow new Error(\n\t\t\t\t\t'Set operator error (union / intersect / except): selected fields are not the same or are in a different order',\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\n\t\treturn (leftSelect as AnyPgSelect).addSetOperators(setOperators) as any;\n\t};\n}\n\nconst getPgSetOperators = () => ({\n\tunion,\n\tunionAll,\n\tintersect,\n\tintersectAll,\n\texcept,\n\texceptAll,\n});\n\n/**\n * Adds `union` set operator to the query.\n *\n * Calling this method will combine the result sets of the `select` statements and remove any duplicate rows that appear across them.\n *\n * See docs: {@link https://orm.drizzle.team/docs/set-operations#union}\n *\n * @example\n *\n * ```ts\n * // Select all unique names from customers and users tables\n * import { union } from 'drizzle-orm/pg-core'\n *\n * await union(\n *   db.select({ name: users.name }).from(users),\n *   db.select({ name: customers.name }).from(customers)\n * );\n * // or\n * await db.select({ name: users.name })\n *   .from(users)\n *   .union(\n *     db.select({ name: customers.name }).from(customers)\n *   );\n * ```\n */\nexport const union = createSetOperator('union', false);\n\n/**\n * Adds `union all` set operator to the query.\n *\n * Calling this method will combine the result-set of the `select` statements and keep all duplicate rows that appear across them.\n *\n * See docs: {@link https://orm.drizzle.team/docs/set-operations#union-all}\n *\n * @example\n *\n * ```ts\n * // Select all transaction ids from both online and in-store sales\n * import { unionAll } from 'drizzle-orm/pg-core'\n *\n * await unionAll(\n *   db.select({ transaction: onlineSales.transactionId }).from(onlineSales),\n *   db.select({ transaction: inStoreSales.transactionId }).from(inStoreSales)\n * );\n * // or\n * await db.select({ transaction: onlineSales.transactionId })\n *   .from(onlineSales)\n *   .unionAll(\n *     db.select({ transaction: inStoreSales.transactionId }).from(inStoreSales)\n *   );\n * ```\n */\nexport const unionAll = createSetOperator('union', true);\n\n/**\n * Adds `intersect` set operator to the query.\n *\n * Calling this method will retain only the rows that are present in both result sets and eliminate duplicates.\n *\n * See docs: {@link https://orm.drizzle.team/docs/set-operations#intersect}\n *\n * @example\n *\n * ```ts\n * // Select course names that are offered in both departments A and B\n * import { intersect } from 'drizzle-orm/pg-core'\n *\n * await intersect(\n *   db.select({ courseName: depA.courseName }).from(depA),\n *   db.select({ courseName: depB.courseName }).from(depB)\n * );\n * // or\n * await db.select({ courseName: depA.courseName })\n *   .from(depA)\n *   .intersect(\n *     db.select({ courseName: depB.courseName }).from(depB)\n *   );\n * ```\n */\nexport const intersect = createSetOperator('intersect', false);\n\n/**\n * Adds `intersect all` set operator to the query.\n *\n * Calling this method will retain only the rows that are present in both result sets including all duplicates.\n *\n * See docs: {@link https://orm.drizzle.team/docs/set-operations#intersect-all}\n *\n * @example\n *\n * ```ts\n * // Select all products and quantities that are ordered by both regular and VIP customers\n * import { intersectAll } from 'drizzle-orm/pg-core'\n *\n * await intersectAll(\n *   db.select({\n *     productId: regularCustomerOrders.productId,\n *     quantityOrdered: regularCustomerOrders.quantityOrdered\n *   })\n *   .from(regularCustomerOrders),\n *   db.select({\n *     productId: vipCustomerOrders.productId,\n *     quantityOrdered: vipCustomerOrders.quantityOrdered\n *   })\n *   .from(vipCustomerOrders)\n * );\n * // or\n * await db.select({\n *   productId: regularCustomerOrders.productId,\n *   quantityOrdered: regularCustomerOrders.quantityOrdered\n * })\n * .from(regularCustomerOrders)\n * .intersectAll(\n *   db.select({\n *     productId: vipCustomerOrders.productId,\n *     quantityOrdered: vipCustomerOrders.quantityOrdered\n *   })\n *   .from(vipCustomerOrders)\n * );\n * ```\n */\nexport const intersectAll = createSetOperator('intersect', true);\n\n/**\n * Adds `except` set operator to the query.\n *\n * Calling this method will retrieve all unique rows from the left query, except for the rows that are present in the result set of the right query.\n *\n * See docs: {@link https://orm.drizzle.team/docs/set-operations#except}\n *\n * @example\n *\n * ```ts\n * // Select all courses offered in department A but not in department B\n * import { except } from 'drizzle-orm/pg-core'\n *\n * await except(\n *   db.select({ courseName: depA.courseName }).from(depA),\n *   db.select({ courseName: depB.courseName }).from(depB)\n * );\n * // or\n * await db.select({ courseName: depA.courseName })\n *   .from(depA)\n *   .except(\n *     db.select({ courseName: depB.courseName }).from(depB)\n *   );\n * ```\n */\nexport const except = createSetOperator('except', false);\n\n/**\n * Adds `except all` set operator to the query.\n *\n * Calling this method will retrieve all rows from the left query, except for the rows that are present in the result set of the right query.\n *\n * See docs: {@link https://orm.drizzle.team/docs/set-operations#except-all}\n *\n * @example\n *\n * ```ts\n * // Select all products that are ordered by regular customers but not by VIP customers\n * import { exceptAll } from 'drizzle-orm/pg-core'\n *\n * await exceptAll(\n *   db.select({\n *     productId: regularCustomerOrders.productId,\n *     quantityOrdered: regularCustomerOrders.quantityOrdered\n *   })\n *   .from(regularCustomerOrders),\n *   db.select({\n *     productId: vipCustomerOrders.productId,\n *     quantityOrdered: vipCustomerOrders.quantityOrdered\n *   })\n *   .from(vipCustomerOrders)\n * );\n * // or\n * await db.select({\n *   productId: regularCustomerOrders.productId,\n *   quantityOrdered: regularCustomerOrders.quantityOrdered,\n * })\n * .from(regularCustomerOrders)\n * .exceptAll(\n *   db.select({\n *     productId: vipCustomerOrders.productId,\n *     quantityOrdered: vipCustomerOrders.quantityOrdered,\n *   })\n *   .from(vipCustomerOrders)\n * );\n * ```\n */\nexport const exceptAll = createSetOperator('except', true);\n", "import { entityKind, is } from '~/entity.ts';\nimport type { PgDialectConfig } from '~/pg-core/dialect.ts';\nimport { PgDialect } from '~/pg-core/dialect.ts';\nimport type { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport { SelectionProxyHandler } from '~/selection-proxy.ts';\nimport type { ColumnsSelection, SQL, SQLWrapper } from '~/sql/sql.ts';\nimport { WithSubquery } from '~/subquery.ts';\nimport type { PgColumn } from '../columns/index.ts';\nimport type { WithBuilder } from '../subquery.ts';\nimport { PgSelectBuilder } from './select.ts';\nimport type { SelectedFields } from './select.types.ts';\n\nexport class QueryBuilder {\n\tstatic readonly [entityKind]: string = 'PgQueryBuilder';\n\n\tprivate dialect: PgDialect | undefined;\n\tprivate dialectConfig: PgDialectConfig | undefined;\n\n\tconstructor(dialect?: PgDialect | PgDialectConfig) {\n\t\tthis.dialect = is(dialect, PgDialect) ? dialect : undefined;\n\t\tthis.dialectConfig = is(dialect, PgDialect) ? undefined : dialect;\n\t}\n\n\t$with: WithBuilder = (alias: string, selection?: ColumnsSelection) => {\n\t\tconst queryBuilder = this;\n\t\tconst as = (\n\t\t\tqb:\n\t\t\t\t| TypedQueryBuilder<ColumnsSelection | undefined>\n\t\t\t\t| SQL\n\t\t\t\t| ((qb: QueryBuilder) => TypedQueryBuilder<ColumnsSelection | undefined> | SQL),\n\t\t) => {\n\t\t\tif (typeof qb === 'function') {\n\t\t\t\tqb = qb(queryBuilder);\n\t\t\t}\n\n\t\t\treturn new Proxy(\n\t\t\t\tnew WithSubquery(\n\t\t\t\t\tqb.getSQL(),\n\t\t\t\t\tselection ?? ('getSelectedFields' in qb ? qb.getSelectedFields() ?? {} : {}) as SelectedFields,\n\t\t\t\t\talias,\n\t\t\t\t\ttrue,\n\t\t\t\t),\n\t\t\t\tnew SelectionProxyHandler({ alias, sqlAliasedBehavior: 'alias', sqlBehavior: 'error' }),\n\t\t\t) as any;\n\t\t};\n\t\treturn { as };\n\t};\n\n\twith(...queries: WithSubquery[]) {\n\t\tconst self = this;\n\n\t\tfunction select(): PgSelectBuilder<undefined, 'qb'>;\n\t\tfunction select<TSelection extends SelectedFields>(fields: TSelection): PgSelectBuilder<TSelection, 'qb'>;\n\t\tfunction select<TSelection extends SelectedFields>(\n\t\t\tfields?: TSelection,\n\t\t): PgSelectBuilder<TSelection | undefined, 'qb'> {\n\t\t\treturn new PgSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: undefined,\n\t\t\t\tdialect: self.getDialect(),\n\t\t\t\twithList: queries,\n\t\t\t});\n\t\t}\n\n\t\tfunction selectDistinct(): PgSelectBuilder<undefined, 'qb'>;\n\t\tfunction selectDistinct<TSelection extends SelectedFields>(fields: TSelection): PgSelectBuilder<TSelection, 'qb'>;\n\t\tfunction selectDistinct<TSelection extends SelectedFields>(\n\t\t\tfields?: TSelection,\n\t\t): PgSelectBuilder<TSelection | undefined, 'qb'> {\n\t\t\treturn new PgSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: undefined,\n\t\t\t\tdialect: self.getDialect(),\n\t\t\t\tdistinct: true,\n\t\t\t});\n\t\t}\n\n\t\tfunction selectDistinctOn(on: (PgColumn | SQLWrapper)[]): PgSelectBuilder<undefined, 'qb'>;\n\t\tfunction selectDistinctOn<TSelection extends SelectedFields>(\n\t\t\ton: (PgColumn | SQLWrapper)[],\n\t\t\tfields: TSelection,\n\t\t): PgSelectBuilder<TSelection, 'qb'>;\n\t\tfunction selectDistinctOn<TSelection extends SelectedFields>(\n\t\t\ton: (PgColumn | SQLWrapper)[],\n\t\t\tfields?: TSelection,\n\t\t): PgSelectBuilder<TSelection | undefined, 'qb'> {\n\t\t\treturn new PgSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: undefined,\n\t\t\t\tdialect: self.getDialect(),\n\t\t\t\tdistinct: { on },\n\t\t\t});\n\t\t}\n\n\t\treturn { select, selectDistinct, selectDistinctOn };\n\t}\n\n\tselect(): PgSelectBuilder<undefined, 'qb'>;\n\tselect<TSelection extends SelectedFields>(fields: TSelection): PgSelectBuilder<TSelection, 'qb'>;\n\tselect<TSelection extends SelectedFields>(fields?: TSelection): PgSelectBuilder<TSelection | undefined, 'qb'> {\n\t\treturn new PgSelectBuilder({\n\t\t\tfields: fields ?? undefined,\n\t\t\tsession: undefined,\n\t\t\tdialect: this.getDialect(),\n\t\t});\n\t}\n\n\tselectDistinct(): PgSelectBuilder<undefined>;\n\tselectDistinct<TSelection extends SelectedFields>(fields: TSelection): PgSelectBuilder<TSelection>;\n\tselectDistinct<TSelection extends SelectedFields>(fields?: TSelection): PgSelectBuilder<TSelection | undefined> {\n\t\treturn new PgSelectBuilder({\n\t\t\tfields: fields ?? undefined,\n\t\t\tsession: undefined,\n\t\t\tdialect: this.getDialect(),\n\t\t\tdistinct: true,\n\t\t});\n\t}\n\n\tselectDistinctOn(on: (PgColumn | SQLWrapper)[]): PgSelectBuilder<undefined>;\n\tselectDistinctOn<TSelection extends SelectedFields>(\n\t\ton: (PgColumn | SQLWrapper)[],\n\t\tfields: TSelection,\n\t): PgSelectBuilder<TSelection>;\n\tselectDistinctOn<TSelection extends SelectedFields>(\n\t\ton: (PgColumn | SQLWrapper)[],\n\t\tfields?: TSelection,\n\t): PgSelectBuilder<TSelection | undefined> {\n\t\treturn new PgSelectBuilder({\n\t\t\tfields: fields ?? undefined,\n\t\t\tsession: undefined,\n\t\t\tdialect: this.getDialect(),\n\t\t\tdistinct: { on },\n\t\t});\n\t}\n\n\t// Lazy load dialect to avoid circular dependency\n\tprivate getDialect() {\n\t\tif (!this.dialect) {\n\t\t\tthis.dialect = new PgDialect(this.dialectConfig);\n\t\t}\n\n\t\treturn this.dialect;\n\t}\n}\n", "import { entityKind, is } from '~/entity.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport type { IndexColumn } from '~/pg-core/indexes.ts';\nimport type {\n\tPgPreparedQuery,\n\tPgQueryResultHKT,\n\tPgQueryResultKind,\n\tPgSession,\n\tPreparedQueryConfig,\n} from '~/pg-core/session.ts';\nimport type { PgTable, TableConfig } from '~/pg-core/table.ts';\nimport type { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport type { SelectResultFields } from '~/query-builders/select.types.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport { SelectionProxyHandler } from '~/selection-proxy.ts';\nimport type { ColumnsSelection, Placeholder, Query, SQLWrapper } from '~/sql/sql.ts';\nimport { Param, SQL, sql } from '~/sql/sql.ts';\nimport type { Subquery } from '~/subquery.ts';\nimport type { InferInsertModel } from '~/table.ts';\nimport { Columns, getTableName, Table } from '~/table.ts';\nimport { tracer } from '~/tracing.ts';\nimport { haveSameKeys, mapUpdateSet, type NeonAuthToken, orderSelectedFields } from '~/utils.ts';\nimport type { AnyPgColumn, PgColumn } from '../columns/common.ts';\nimport { QueryBuilder } from './query-builder.ts';\nimport type { SelectedFieldsFlat, SelectedFieldsOrdered } from './select.types.ts';\nimport type { PgUpdateSetSource } from './update.ts';\n\nexport interface PgInsertConfig<TTable extends PgTable = PgTable> {\n\ttable: TTable;\n\tvalues: Record<string, Param | SQL>[] | PgInsertSelectQueryBuilder<TTable> | SQL;\n\twithList?: Subquery[];\n\tonConflict?: SQL;\n\treturningFields?: SelectedFieldsFlat;\n\treturning?: SelectedFieldsOrdered;\n\tselect?: boolean;\n\toverridingSystemValue_?: boolean;\n}\n\nexport type PgInsertValue<TTable extends PgTable<TableConfig>, OverrideT extends boolean = false> =\n\t& {\n\t\t[Key in keyof InferInsertModel<TTable, { dbColumnNames: false; override: OverrideT }>]:\n\t\t\t| InferInsertModel<TTable, { dbColumnNames: false; override: OverrideT }>[Key]\n\t\t\t| SQL\n\t\t\t| Placeholder;\n\t}\n\t& {};\n\nexport type PgInsertSelectQueryBuilder<TTable extends PgTable> = TypedQueryBuilder<\n\t{ [K in keyof TTable['$inferInsert']]: AnyPgColumn | SQL | SQL.Aliased | TTable['$inferInsert'][K] }\n>;\n\nexport class PgInsertBuilder<\n\tTTable extends PgTable,\n\tTQueryResult extends PgQueryResultHKT,\n\tOverrideT extends boolean = false,\n> {\n\tstatic readonly [entityKind]: string = 'PgInsertBuilder';\n\n\tconstructor(\n\t\tprivate table: TTable,\n\t\tprivate session: PgSession,\n\t\tprivate dialect: PgDialect,\n\t\tprivate withList?: Subquery[],\n\t\tprivate overridingSystemValue_?: boolean,\n\t) {}\n\n\tprivate authToken?: NeonAuthToken;\n\t/** @internal */\n\tsetToken(token?: NeonAuthToken) {\n\t\tthis.authToken = token;\n\t\treturn this;\n\t}\n\n\toverridingSystemValue(): Omit<PgInsertBuilder<TTable, TQueryResult, true>, 'overridingSystemValue'> {\n\t\tthis.overridingSystemValue_ = true;\n\t\treturn this as any;\n\t}\n\n\tvalues(value: PgInsertValue<TTable, OverrideT>): PgInsertBase<TTable, TQueryResult>;\n\tvalues(values: PgInsertValue<TTable, OverrideT>[]): PgInsertBase<TTable, TQueryResult>;\n\tvalues(\n\t\tvalues: PgInsertValue<TTable, OverrideT> | PgInsertValue<TTable, OverrideT>[],\n\t): PgInsertBase<TTable, TQueryResult> {\n\t\tvalues = Array.isArray(values) ? values : [values];\n\t\tif (values.length === 0) {\n\t\t\tthrow new Error('values() must be called with at least one value');\n\t\t}\n\t\tconst mappedValues = values.map((entry) => {\n\t\t\tconst result: Record<string, Param | SQL> = {};\n\t\t\tconst cols = this.table[Table.Symbol.Columns];\n\t\t\tfor (const colKey of Object.keys(entry)) {\n\t\t\t\tconst colValue = entry[colKey as keyof typeof entry];\n\t\t\t\tresult[colKey] = is(colValue, SQL) ? colValue : new Param(colValue, cols[colKey]);\n\t\t\t}\n\t\t\treturn result;\n\t\t});\n\n\t\treturn new PgInsertBase(\n\t\t\tthis.table,\n\t\t\tmappedValues,\n\t\t\tthis.session,\n\t\t\tthis.dialect,\n\t\t\tthis.withList,\n\t\t\tfalse,\n\t\t\tthis.overridingSystemValue_,\n\t\t).setToken(this.authToken) as any;\n\t}\n\n\tselect(selectQuery: (qb: QueryBuilder) => PgInsertSelectQueryBuilder<TTable>): PgInsertBase<TTable, TQueryResult>;\n\tselect(selectQuery: (qb: QueryBuilder) => SQL): PgInsertBase<TTable, TQueryResult>;\n\tselect(selectQuery: SQL): PgInsertBase<TTable, TQueryResult>;\n\tselect(selectQuery: PgInsertSelectQueryBuilder<TTable>): PgInsertBase<TTable, TQueryResult>;\n\tselect(\n\t\tselectQuery:\n\t\t\t| SQL\n\t\t\t| PgInsertSelectQueryBuilder<TTable>\n\t\t\t| ((qb: QueryBuilder) => PgInsertSelectQueryBuilder<TTable> | SQL),\n\t): PgInsertBase<TTable, TQueryResult> {\n\t\tconst select = typeof selectQuery === 'function' ? selectQuery(new QueryBuilder()) : selectQuery;\n\n\t\tif (\n\t\t\t!is(select, SQL)\n\t\t\t&& !haveSameKeys(this.table[Columns], select._.selectedFields)\n\t\t) {\n\t\t\tthrow new Error(\n\t\t\t\t'Insert select error: selected fields are not the same or are in a different order compared to the table definition',\n\t\t\t);\n\t\t}\n\n\t\treturn new PgInsertBase(this.table, select, this.session, this.dialect, this.withList, true);\n\t}\n}\n\nexport type PgInsertWithout<T extends AnyPgInsert, TDynamic extends boolean, K extends keyof T & string> =\n\tTDynamic extends true ? T\n\t\t: Omit<\n\t\t\tPgInsertBase<\n\t\t\t\tT['_']['table'],\n\t\t\t\tT['_']['queryResult'],\n\t\t\t\tT['_']['selectedFields'],\n\t\t\t\tT['_']['returning'],\n\t\t\t\tTDynamic,\n\t\t\t\tT['_']['excludedMethods'] | K\n\t\t\t>,\n\t\t\tT['_']['excludedMethods'] | K\n\t\t>;\n\nexport type PgInsertReturning<\n\tT extends AnyPgInsert,\n\tTDynamic extends boolean,\n\tTSelectedFields extends SelectedFieldsFlat,\n> = PgInsertBase<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tTSelectedFields,\n\tSelectResultFields<TSelectedFields>,\n\tTDynamic,\n\tT['_']['excludedMethods']\n>;\n\nexport type PgInsertReturningAll<T extends AnyPgInsert, TDynamic extends boolean> = PgInsertBase<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tT['_']['table']['_']['columns'],\n\tT['_']['table']['$inferSelect'],\n\tTDynamic,\n\tT['_']['excludedMethods']\n>;\n\nexport interface PgInsertOnConflictDoUpdateConfig<T extends AnyPgInsert> {\n\ttarget: IndexColumn | IndexColumn[];\n\t/** @deprecated use either `targetWhere` or `setWhere` */\n\twhere?: SQL;\n\t// TODO: add tests for targetWhere and setWhere\n\ttargetWhere?: SQL;\n\tsetWhere?: SQL;\n\tset: PgUpdateSetSource<T['_']['table']>;\n}\n\nexport type PgInsertPrepare<T extends AnyPgInsert> = PgPreparedQuery<\n\tPreparedQueryConfig & {\n\t\texecute: T['_']['returning'] extends undefined ? PgQueryResultKind<T['_']['queryResult'], never>\n\t\t\t: T['_']['returning'][];\n\t}\n>;\n\nexport type PgInsertDynamic<T extends AnyPgInsert> = PgInsert<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tT['_']['returning']\n>;\n\nexport type AnyPgInsert = PgInsertBase<any, any, any, any, any, any>;\n\nexport type PgInsert<\n\tTTable extends PgTable = PgTable,\n\tTQueryResult extends PgQueryResultHKT = PgQueryResultHKT,\n\tTSelectedFields extends ColumnsSelection | undefined = ColumnsSelection | undefined,\n\tTReturning extends Record<string, unknown> | undefined = Record<string, unknown> | undefined,\n> = PgInsertBase<TTable, TQueryResult, TSelectedFields, TReturning, true, never>;\n\nexport interface PgInsertBase<\n\tTTable extends PgTable,\n\tTQueryResult extends PgQueryResultHKT,\n\tTSelectedFields extends ColumnsSelection | undefined = undefined,\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n> extends\n\tTypedQueryBuilder<\n\t\tTSelectedFields,\n\t\tTReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[]\n\t>,\n\tQueryPromise<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[]>,\n\tRunnableQuery<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[], 'pg'>,\n\tSQLWrapper\n{\n\treadonly _: {\n\t\treadonly dialect: 'pg';\n\t\treadonly table: TTable;\n\t\treadonly queryResult: TQueryResult;\n\t\treadonly selectedFields: TSelectedFields;\n\t\treadonly returning: TReturning;\n\t\treadonly dynamic: TDynamic;\n\t\treadonly excludedMethods: TExcludedMethods;\n\t\treadonly result: TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[];\n\t};\n}\n\nexport class PgInsertBase<\n\tTTable extends PgTable,\n\tTQueryResult extends PgQueryResultHKT,\n\tTSelectedFields extends ColumnsSelection | undefined = undefined,\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTDynamic extends boolean = false,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTExcludedMethods extends string = never,\n> extends QueryPromise<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[]>\n\timplements\n\t\tTypedQueryBuilder<\n\t\t\tTSelectedFields,\n\t\t\tTReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[]\n\t\t>,\n\t\tRunnableQuery<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[], 'pg'>,\n\t\tSQLWrapper\n{\n\tstatic override readonly [entityKind]: string = 'PgInsert';\n\n\tprivate config: PgInsertConfig<TTable>;\n\n\tconstructor(\n\t\ttable: TTable,\n\t\tvalues: PgInsertConfig['values'],\n\t\tprivate session: PgSession,\n\t\tprivate dialect: PgDialect,\n\t\twithList?: Subquery[],\n\t\tselect?: boolean,\n\t\toverridingSystemValue_?: boolean,\n\t) {\n\t\tsuper();\n\t\tthis.config = { table, values: values as any, withList, select, overridingSystemValue_ };\n\t}\n\n\t/**\n\t * Adds a `returning` clause to the query.\n\t *\n\t * Calling this method will return the specified fields of the inserted rows. If no fields are specified, all fields will be returned.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/insert#insert-returning}\n\t *\n\t * @example\n\t * ```ts\n\t * // Insert one row and return all fields\n\t * const insertedCar: Car[] = await db.insert(cars)\n\t *   .values({ brand: 'BMW' })\n\t *   .returning();\n\t *\n\t * // Insert one row and return only the id\n\t * const insertedCarId: { id: number }[] = await db.insert(cars)\n\t *   .values({ brand: 'BMW' })\n\t *   .returning({ id: cars.id });\n\t * ```\n\t */\n\treturning(): PgInsertWithout<PgInsertReturningAll<this, TDynamic>, TDynamic, 'returning'>;\n\treturning<TSelectedFields extends SelectedFieldsFlat>(\n\t\tfields: TSelectedFields,\n\t): PgInsertWithout<PgInsertReturning<this, TDynamic, TSelectedFields>, TDynamic, 'returning'>;\n\treturning(\n\t\tfields: SelectedFieldsFlat = this.config.table[Table.Symbol.Columns],\n\t): PgInsertWithout<AnyPgInsert, TDynamic, 'returning'> {\n\t\tthis.config.returningFields = fields;\n\t\tthis.config.returning = orderSelectedFields<PgColumn>(fields);\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds an `on conflict do nothing` clause to the query.\n\t *\n\t * Calling this method simply avoids inserting a row as its alternative action.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/insert#on-conflict-do-nothing}\n\t *\n\t * @param config The `target` and `where` clauses.\n\t *\n\t * @example\n\t * ```ts\n\t * // Insert one row and cancel the insert if there's a conflict\n\t * await db.insert(cars)\n\t *   .values({ id: 1, brand: 'BMW' })\n\t *   .onConflictDoNothing();\n\t *\n\t * // Explicitly specify conflict target\n\t * await db.insert(cars)\n\t *   .values({ id: 1, brand: 'BMW' })\n\t *   .onConflictDoNothing({ target: cars.id });\n\t * ```\n\t */\n\tonConflictDoNothing(\n\t\tconfig: { target?: IndexColumn | IndexColumn[]; where?: SQL } = {},\n\t): PgInsertWithout<this, TDynamic, 'onConflictDoNothing' | 'onConflictDoUpdate'> {\n\t\tif (config.target === undefined) {\n\t\t\tthis.config.onConflict = sql`do nothing`;\n\t\t} else {\n\t\t\tlet targetColumn = '';\n\t\t\ttargetColumn = Array.isArray(config.target)\n\t\t\t\t? config.target.map((it) => this.dialect.escapeName(this.dialect.casing.getColumnCasing(it))).join(',')\n\t\t\t\t: this.dialect.escapeName(this.dialect.casing.getColumnCasing(config.target));\n\n\t\t\tconst whereSql = config.where ? sql` where ${config.where}` : undefined;\n\t\t\tthis.config.onConflict = sql`(${sql.raw(targetColumn)})${whereSql} do nothing`;\n\t\t}\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds an `on conflict do update` clause to the query.\n\t *\n\t * Calling this method will update the existing row that conflicts with the row proposed for insertion as its alternative action.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/insert#upserts-and-conflicts}\n\t *\n\t * @param config The `target`, `set` and `where` clauses.\n\t *\n\t * @example\n\t * ```ts\n\t * // Update the row if there's a conflict\n\t * await db.insert(cars)\n\t *   .values({ id: 1, brand: 'BMW' })\n\t *   .onConflictDoUpdate({\n\t *     target: cars.id,\n\t *     set: { brand: 'Porsche' }\n\t *   });\n\t *\n\t * // Upsert with 'where' clause\n\t * await db.insert(cars)\n\t *   .values({ id: 1, brand: 'BMW' })\n\t *   .onConflictDoUpdate({\n\t *     target: cars.id,\n\t *     set: { brand: 'newBMW' },\n\t *     targetWhere: sql`${cars.createdAt} > '2023-01-01'::date`,\n\t *   });\n\t * ```\n\t */\n\tonConflictDoUpdate(\n\t\tconfig: PgInsertOnConflictDoUpdateConfig<this>,\n\t): PgInsertWithout<this, TDynamic, 'onConflictDoNothing' | 'onConflictDoUpdate'> {\n\t\tif (config.where && (config.targetWhere || config.setWhere)) {\n\t\t\tthrow new Error(\n\t\t\t\t'You cannot use both \"where\" and \"targetWhere\"/\"setWhere\" at the same time - \"where\" is deprecated, use \"targetWhere\" or \"setWhere\" instead.',\n\t\t\t);\n\t\t}\n\t\tconst whereSql = config.where ? sql` where ${config.where}` : undefined;\n\t\tconst targetWhereSql = config.targetWhere ? sql` where ${config.targetWhere}` : undefined;\n\t\tconst setWhereSql = config.setWhere ? sql` where ${config.setWhere}` : undefined;\n\t\tconst setSql = this.dialect.buildUpdateSet(this.config.table, mapUpdateSet(this.config.table, config.set));\n\t\tlet targetColumn = '';\n\t\ttargetColumn = Array.isArray(config.target)\n\t\t\t? config.target.map((it) => this.dialect.escapeName(this.dialect.casing.getColumnCasing(it))).join(',')\n\t\t\t: this.dialect.escapeName(this.dialect.casing.getColumnCasing(config.target));\n\t\tthis.config.onConflict = sql`(${\n\t\t\tsql.raw(targetColumn)\n\t\t})${targetWhereSql} do update set ${setSql}${whereSql}${setWhereSql}`;\n\t\treturn this as any;\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this.dialect.buildInsertQuery(this.config);\n\t}\n\n\ttoSQL(): Query {\n\t\tconst { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n\t\treturn rest;\n\t}\n\n\t/** @internal */\n\t_prepare(name?: string): PgInsertPrepare<this> {\n\t\treturn tracer.startActiveSpan('drizzle.prepareQuery', () => {\n\t\t\treturn this.session.prepareQuery<\n\t\t\t\tPreparedQueryConfig & {\n\t\t\t\t\texecute: TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[];\n\t\t\t\t}\n\t\t\t>(this.dialect.sqlToQuery(this.getSQL()), this.config.returning, name, true);\n\t\t});\n\t}\n\n\tprepare(name: string): PgInsertPrepare<this> {\n\t\treturn this._prepare(name);\n\t}\n\n\tprivate authToken?: NeonAuthToken;\n\t/** @internal */\n\tsetToken(token?: NeonAuthToken) {\n\t\tthis.authToken = token;\n\t\treturn this;\n\t}\n\n\toverride execute: ReturnType<this['prepare']>['execute'] = (placeholderValues) => {\n\t\treturn tracer.startActiveSpan('drizzle.operation', () => {\n\t\t\treturn this._prepare().execute(placeholderValues, this.authToken);\n\t\t});\n\t};\n\n\t/** @internal */\n\tgetSelectedFields(): this['_']['selectedFields'] {\n\t\treturn (\n\t\t\tthis.config.returningFields\n\t\t\t\t? new Proxy(\n\t\t\t\t\tthis.config.returningFields,\n\t\t\t\t\tnew SelectionProxyHandler({\n\t\t\t\t\t\talias: getTableName(this.config.table),\n\t\t\t\t\t\tsqlAliasedBehavior: 'alias',\n\t\t\t\t\t\tsqlBehavior: 'error',\n\t\t\t\t\t}),\n\t\t\t\t)\n\t\t\t\t: undefined\n\t\t) as this['_']['selectedFields'];\n\t}\n\n\t$dynamic(): PgInsertDynamic<this> {\n\t\treturn this as any;\n\t}\n}\n", "import { entityKind } from '~/entity.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport type {\n\tPgPreparedQuery,\n\tPgQueryResultHKT,\n\tPgQueryResultKind,\n\tPgSession,\n\tPreparedQueryConfig,\n} from '~/pg-core/session.ts';\nimport type { PgMaterializedView } from '~/pg-core/view.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport type { Query, SQL, SQLWrapper } from '~/sql/sql.ts';\nimport { tracer } from '~/tracing.ts';\nimport type { NeonAuthToken } from '~/utils';\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface PgRefreshMaterializedView<TQueryResult extends PgQueryResultHKT>\n\textends\n\t\tQueryPromise<PgQueryResultKind<TQueryResult, never>>,\n\t\tRunnableQuery<PgQueryResultKind<TQueryResult, never>, 'pg'>,\n\t\tSQLWrapper\n{\n\treadonly _: {\n\t\treadonly dialect: 'pg';\n\t\treadonly result: PgQueryResultKind<TQueryResult, never>;\n\t};\n}\n\nexport class PgRefreshMaterializedView<TQueryResult extends PgQueryResultHKT>\n\textends QueryPromise<PgQueryResultKind<TQueryResult, never>>\n\timplements RunnableQuery<PgQueryResultKind<TQueryResult, never>, 'pg'>, SQLWrapper\n{\n\tstatic override readonly [entityKind]: string = 'PgRefreshMaterializedView';\n\n\tprivate config: {\n\t\tview: PgMaterializedView;\n\t\tconcurrently?: boolean;\n\t\twithNoData?: boolean;\n\t};\n\n\tconstructor(\n\t\tview: PgMaterializedView,\n\t\tprivate session: PgSession,\n\t\tprivate dialect: PgDialect,\n\t) {\n\t\tsuper();\n\t\tthis.config = { view };\n\t}\n\n\tconcurrently(): this {\n\t\tif (this.config.withNoData !== undefined) {\n\t\t\tthrow new Error('Cannot use concurrently and withNoData together');\n\t\t}\n\t\tthis.config.concurrently = true;\n\t\treturn this;\n\t}\n\n\twithNoData(): this {\n\t\tif (this.config.concurrently !== undefined) {\n\t\t\tthrow new Error('Cannot use concurrently and withNoData together');\n\t\t}\n\t\tthis.config.withNoData = true;\n\t\treturn this;\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this.dialect.buildRefreshMaterializedViewQuery(this.config);\n\t}\n\n\ttoSQL(): Query {\n\t\tconst { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n\t\treturn rest;\n\t}\n\n\t/** @internal */\n\t_prepare(name?: string): PgPreparedQuery<\n\t\tPreparedQueryConfig & {\n\t\t\texecute: PgQueryResultKind<TQueryResult, never>;\n\t\t}\n\t> {\n\t\treturn tracer.startActiveSpan('drizzle.prepareQuery', () => {\n\t\t\treturn this.session.prepareQuery(this.dialect.sqlToQuery(this.getSQL()), undefined, name, true);\n\t\t});\n\t}\n\n\tprepare(name: string): PgPreparedQuery<\n\t\tPreparedQueryConfig & {\n\t\t\texecute: PgQueryResultKind<TQueryResult, never>;\n\t\t}\n\t> {\n\t\treturn this._prepare(name);\n\t}\n\n\tprivate authToken?: NeonAuthToken;\n\t/** @internal */\n\tsetToken(token: NeonAuthToken) {\n\t\tthis.authToken = token;\n\t\treturn this;\n\t}\n\n\texecute: ReturnType<this['prepare']>['execute'] = (placeholderValues) => {\n\t\treturn tracer.startActiveSpan('drizzle.operation', () => {\n\t\t\treturn this._prepare().execute(placeholderValues, this.authToken);\n\t\t});\n\t};\n}\n", "import type { GetColumnData } from '~/column.ts';\nimport { entityKind, is } from '~/entity.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport type {\n\tPgPreparedQuery,\n\tPgQueryResultHKT,\n\tPgQueryResultKind,\n\tPgSession,\n\tPreparedQueryConfig,\n} from '~/pg-core/session.ts';\nimport { PgTable } from '~/pg-core/table.ts';\nimport { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport type {\n\tAppendToNullabilityMap,\n\tAppendToResult,\n\tGetSelectTableName,\n\tGetSelectTableSelection,\n\tJoinNullability,\n\tJoinType,\n\tSelectMode,\n\tSelectResult,\n} from '~/query-builders/select.types.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport { SelectionProxyHandler } from '~/selection-proxy.ts';\nimport { type ColumnsSelection, type Query, SQL, type SQLWrapper } from '~/sql/sql.ts';\nimport { Subquery } from '~/subquery.ts';\nimport { getTableName, Table } from '~/table.ts';\nimport {\n\ttype Assume,\n\tDrizzleTypeError,\n\tEqual,\n\tgetTableLikeName,\n\tmapUpdateSet,\n\ttype NeonAuthToken,\n\torderSelectedFields,\n\tSimplify,\n\ttype UpdateSet,\n} from '~/utils.ts';\nimport { ViewBaseConfig } from '~/view-common.ts';\nimport type { PgColumn } from '../columns/common.ts';\nimport type { PgViewBase } from '../view-base.ts';\nimport type {\n\tPgSelectJoinConfig,\n\tSelectedFields,\n\tSelectedFieldsOrdered,\n\tTableLikeHasEmptySelection,\n} from './select.types.ts';\n\nexport interface PgUpdateConfig {\n\twhere?: SQL | undefined;\n\tset: UpdateSet;\n\ttable: PgTable;\n\tfrom?: PgTable | Subquery | PgViewBase | SQL;\n\tjoins: PgSelectJoinConfig[];\n\treturningFields?: SelectedFields;\n\treturning?: SelectedFieldsOrdered;\n\twithList?: Subquery[];\n}\n\nexport type PgUpdateSetSource<TTable extends PgTable> =\n\t& {\n\t\t[Key in keyof TTable['$inferInsert']]?:\n\t\t\t| GetColumnData<TTable['_']['columns'][Key]>\n\t\t\t| SQL\n\t\t\t| PgColumn\n\t\t\t| undefined;\n\t}\n\t& {};\n\nexport class PgUpdateBuilder<TTable extends PgTable, TQueryResult extends PgQueryResultHKT> {\n\tstatic readonly [entityKind]: string = 'PgUpdateBuilder';\n\n\tdeclare readonly _: {\n\t\treadonly table: TTable;\n\t};\n\n\tconstructor(\n\t\tprivate table: TTable,\n\t\tprivate session: PgSession,\n\t\tprivate dialect: PgDialect,\n\t\tprivate withList?: Subquery[],\n\t) {}\n\n\tprivate authToken?: NeonAuthToken;\n\tsetToken(token: NeonAuthToken) {\n\t\tthis.authToken = token;\n\t\treturn this;\n\t}\n\n\tset(\n\t\tvalues: PgUpdateSetSource<TTable>,\n\t): PgUpdateWithout<PgUpdateBase<TTable, TQueryResult>, false, 'leftJoin' | 'rightJoin' | 'innerJoin' | 'fullJoin'> {\n\t\treturn new PgUpdateBase<TTable, TQueryResult>(\n\t\t\tthis.table,\n\t\t\tmapUpdateSet(this.table, values),\n\t\t\tthis.session,\n\t\t\tthis.dialect,\n\t\t\tthis.withList,\n\t\t).setToken(this.authToken);\n\t}\n}\n\nexport type PgUpdateWithout<\n\tT extends AnyPgUpdate,\n\tTDynamic extends boolean,\n\tK extends keyof T & string,\n> = TDynamic extends true ? T : Omit<\n\tPgUpdateBase<\n\t\tT['_']['table'],\n\t\tT['_']['queryResult'],\n\t\tT['_']['from'],\n\t\tT['_']['selectedFields'],\n\t\tT['_']['returning'],\n\t\tT['_']['nullabilityMap'],\n\t\tT['_']['joins'],\n\t\tTDynamic,\n\t\tT['_']['excludedMethods'] | K\n\t>,\n\tT['_']['excludedMethods'] | K\n>;\n\nexport type PgUpdateWithJoins<\n\tT extends AnyPgUpdate,\n\tTDynamic extends boolean,\n\tTFrom extends PgTable | Subquery | PgViewBase | SQL,\n> = TDynamic extends true ? T : Omit<\n\tPgUpdateBase<\n\t\tT['_']['table'],\n\t\tT['_']['queryResult'],\n\t\tTFrom,\n\t\tT['_']['selectedFields'],\n\t\tT['_']['returning'],\n\t\tAppendToNullabilityMap<T['_']['nullabilityMap'], GetSelectTableName<TFrom>, 'inner'>,\n\t\t[...T['_']['joins'], {\n\t\t\tname: GetSelectTableName<TFrom>;\n\t\t\tjoinType: 'inner';\n\t\t\ttable: TFrom;\n\t\t}],\n\t\tTDynamic,\n\t\tExclude<T['_']['excludedMethods'] | 'from', 'leftJoin' | 'rightJoin' | 'innerJoin' | 'fullJoin'>\n\t>,\n\tExclude<T['_']['excludedMethods'] | 'from', 'leftJoin' | 'rightJoin' | 'innerJoin' | 'fullJoin'>\n>;\n\nexport type PgUpdateJoinFn<\n\tT extends AnyPgUpdate,\n\tTDynamic extends boolean,\n\tTJoinType extends JoinType,\n> = <\n\tTJoinedTable extends PgTable | Subquery | PgViewBase | SQL,\n>(\n\ttable: TableLikeHasEmptySelection<TJoinedTable> extends true ? DrizzleTypeError<\n\t\t\t\"Cannot reference a data-modifying statement subquery if it doesn't contain a `returning` clause\"\n\t\t>\n\t\t: TJoinedTable,\n\ton:\n\t\t| (\n\t\t\t(\n\t\t\t\tupdateTable: T['_']['table']['_']['columns'],\n\t\t\t\tfrom: T['_']['from'] extends PgTable ? T['_']['from']['_']['columns']\n\t\t\t\t\t: T['_']['from'] extends Subquery | PgViewBase ? T['_']['from']['_']['selectedFields']\n\t\t\t\t\t: never,\n\t\t\t) => SQL | undefined\n\t\t)\n\t\t| SQL\n\t\t| undefined,\n) => PgUpdateJoin<T, TDynamic, TJoinType, TJoinedTable>;\n\nexport type PgUpdateJoin<\n\tT extends AnyPgUpdate,\n\tTDynamic extends boolean,\n\tTJoinType extends JoinType,\n\tTJoinedTable extends PgTable | Subquery | PgViewBase | SQL,\n> = TDynamic extends true ? T : PgUpdateBase<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tT['_']['from'],\n\tT['_']['selectedFields'],\n\tT['_']['returning'],\n\tAppendToNullabilityMap<T['_']['nullabilityMap'], GetSelectTableName<TJoinedTable>, TJoinType>,\n\t[...T['_']['joins'], {\n\t\tname: GetSelectTableName<TJoinedTable>;\n\t\tjoinType: TJoinType;\n\t\ttable: TJoinedTable;\n\t}],\n\tTDynamic,\n\tT['_']['excludedMethods']\n>;\n\ntype Join = {\n\tname: string | undefined;\n\tjoinType: JoinType;\n\ttable: PgTable | Subquery | PgViewBase | SQL;\n};\n\ntype AccumulateToResult<\n\tT extends AnyPgUpdate,\n\tTSelectMode extends SelectMode,\n\tTJoins extends Join[],\n\tTSelectedFields extends ColumnsSelection,\n> = TJoins extends [infer TJoin extends Join, ...infer TRest extends Join[]] ? AccumulateToResult<\n\t\tT,\n\t\tTSelectMode extends 'partial' ? TSelectMode : 'multiple',\n\t\tTRest,\n\t\tAppendToResult<\n\t\t\tT['_']['table']['_']['name'],\n\t\t\tTSelectedFields,\n\t\t\tTJoin['name'],\n\t\t\tTJoin['table'] extends Table ? TJoin['table']['_']['columns']\n\t\t\t\t: TJoin['table'] extends Subquery ? Assume<TJoin['table']['_']['selectedFields'], SelectedFields>\n\t\t\t\t: never,\n\t\t\tTSelectMode extends 'partial' ? TSelectMode : 'multiple'\n\t\t>\n\t>\n\t: TSelectedFields;\n\nexport type PgUpdateReturningAll<T extends AnyPgUpdate, TDynamic extends boolean> = PgUpdateWithout<\n\tPgUpdateBase<\n\t\tT['_']['table'],\n\t\tT['_']['queryResult'],\n\t\tT['_']['from'],\n\t\tEqual<T['_']['joins'], []> extends true ? T['_']['table']['_']['columns'] : Simplify<\n\t\t\t& Record<T['_']['table']['_']['name'], T['_']['table']['_']['columns']>\n\t\t\t& {\n\t\t\t\t[K in keyof T['_']['joins'] as T['_']['joins'][K]['table']['_']['name']]:\n\t\t\t\t\tT['_']['joins'][K]['table']['_']['columns'];\n\t\t\t}\n\t\t>,\n\t\tSelectResult<\n\t\t\tAccumulateToResult<\n\t\t\t\tT,\n\t\t\t\t'single',\n\t\t\t\tT['_']['joins'],\n\t\t\t\tGetSelectTableSelection<T['_']['table']>\n\t\t\t>,\n\t\t\t'partial',\n\t\t\tT['_']['nullabilityMap']\n\t\t>,\n\t\tT['_']['nullabilityMap'],\n\t\tT['_']['joins'],\n\t\tTDynamic,\n\t\tT['_']['excludedMethods']\n\t>,\n\tTDynamic,\n\t'returning'\n>;\n\nexport type PgUpdateReturning<\n\tT extends AnyPgUpdate,\n\tTDynamic extends boolean,\n\tTSelectedFields extends SelectedFields,\n> = PgUpdateWithout<\n\tPgUpdateBase<\n\t\tT['_']['table'],\n\t\tT['_']['queryResult'],\n\t\tT['_']['from'],\n\t\tTSelectedFields,\n\t\tSelectResult<\n\t\t\tAccumulateToResult<\n\t\t\t\tT,\n\t\t\t\t'partial',\n\t\t\t\tT['_']['joins'],\n\t\t\t\tTSelectedFields\n\t\t\t>,\n\t\t\t'partial',\n\t\t\tT['_']['nullabilityMap']\n\t\t>,\n\t\tT['_']['nullabilityMap'],\n\t\tT['_']['joins'],\n\t\tTDynamic,\n\t\tT['_']['excludedMethods']\n\t>,\n\tTDynamic,\n\t'returning'\n>;\n\nexport type PgUpdatePrepare<T extends AnyPgUpdate> = PgPreparedQuery<\n\tPreparedQueryConfig & {\n\t\texecute: T['_']['returning'] extends undefined ? PgQueryResultKind<T['_']['queryResult'], never>\n\t\t\t: T['_']['returning'][];\n\t}\n>;\n\nexport type PgUpdateDynamic<T extends AnyPgUpdate> = PgUpdate<\n\tT['_']['table'],\n\tT['_']['queryResult'],\n\tT['_']['from'],\n\tT['_']['returning'],\n\tT['_']['nullabilityMap']\n>;\n\nexport type PgUpdate<\n\tTTable extends PgTable = PgTable,\n\tTQueryResult extends PgQueryResultHKT = PgQueryResultHKT,\n\tTFrom extends PgTable | Subquery | PgViewBase | SQL | undefined = undefined,\n\tTSelectedFields extends ColumnsSelection | undefined = undefined,\n\tTReturning extends Record<string, unknown> | undefined = Record<string, unknown> | undefined,\n\tTNullabilityMap extends Record<string, JoinNullability> = Record<TTable['_']['name'], 'not-null'>,\n\tTJoins extends Join[] = [],\n> = PgUpdateBase<TTable, TQueryResult, TFrom, TSelectedFields, TReturning, TNullabilityMap, TJoins, true, never>;\n\nexport type AnyPgUpdate = PgUpdateBase<any, any, any, any, any, any, any, any, any>;\n\nexport interface PgUpdateBase<\n\tTTable extends PgTable,\n\tTQueryResult extends PgQueryResultHKT,\n\tTFrom extends PgTable | Subquery | PgViewBase | SQL | undefined = undefined,\n\tTSelectedFields extends ColumnsSelection | undefined = undefined,\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n\tTNullabilityMap extends Record<string, JoinNullability> = Record<TTable['_']['name'], 'not-null'>,\n\tTJoins extends Join[] = [],\n\tTDynamic extends boolean = false,\n\tTExcludedMethods extends string = never,\n> extends\n\tTypedQueryBuilder<\n\t\tTSelectedFields,\n\t\tTReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[]\n\t>,\n\tQueryPromise<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[]>,\n\tRunnableQuery<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[], 'pg'>,\n\tSQLWrapper\n{\n\treadonly _: {\n\t\treadonly dialect: 'pg';\n\t\treadonly table: TTable;\n\t\treadonly joins: TJoins;\n\t\treadonly nullabilityMap: TNullabilityMap;\n\t\treadonly queryResult: TQueryResult;\n\t\treadonly from: TFrom;\n\t\treadonly selectedFields: TSelectedFields;\n\t\treadonly returning: TReturning;\n\t\treadonly dynamic: TDynamic;\n\t\treadonly excludedMethods: TExcludedMethods;\n\t\treadonly result: TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[];\n\t};\n}\n\nexport class PgUpdateBase<\n\tTTable extends PgTable,\n\tTQueryResult extends PgQueryResultHKT,\n\tTFrom extends PgTable | Subquery | PgViewBase | SQL | undefined = undefined,\n\tTSelectedFields extends ColumnsSelection | undefined = undefined,\n\tTReturning extends Record<string, unknown> | undefined = undefined,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTNullabilityMap extends Record<string, JoinNullability> = Record<TTable['_']['name'], 'not-null'>,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTJoins extends Join[] = [],\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTDynamic extends boolean = false,\n\t// eslint-disable-next-line @typescript-eslint/no-unused-vars\n\tTExcludedMethods extends string = never,\n> extends QueryPromise<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[]>\n\timplements\n\t\tRunnableQuery<TReturning extends undefined ? PgQueryResultKind<TQueryResult, never> : TReturning[], 'pg'>,\n\t\tSQLWrapper\n{\n\tstatic override readonly [entityKind]: string = 'PgUpdate';\n\n\tprivate config: PgUpdateConfig;\n\tprivate tableName: string | undefined;\n\tprivate joinsNotNullableMap: Record<string, boolean>;\n\n\tconstructor(\n\t\ttable: TTable,\n\t\tset: UpdateSet,\n\t\tprivate session: PgSession,\n\t\tprivate dialect: PgDialect,\n\t\twithList?: Subquery[],\n\t) {\n\t\tsuper();\n\t\tthis.config = { set, table, withList, joins: [] };\n\t\tthis.tableName = getTableLikeName(table);\n\t\tthis.joinsNotNullableMap = typeof this.tableName === 'string' ? { [this.tableName]: true } : {};\n\t}\n\n\tfrom<TFrom extends PgTable | Subquery | PgViewBase | SQL>(\n\t\tsource: TableLikeHasEmptySelection<TFrom> extends true ? DrizzleTypeError<\n\t\t\t\t\"Cannot reference a data-modifying statement subquery if it doesn't contain a `returning` clause\"\n\t\t\t>\n\t\t\t: TFrom,\n\t): PgUpdateWithJoins<this, TDynamic, TFrom> {\n\t\tconst src = source as TFrom;\n\t\tconst tableName = getTableLikeName(src);\n\t\tif (typeof tableName === 'string') {\n\t\t\tthis.joinsNotNullableMap[tableName] = true;\n\t\t}\n\t\tthis.config.from = src;\n\t\treturn this as any;\n\t}\n\n\tprivate getTableLikeFields(table: PgTable | Subquery | PgViewBase): Record<string, unknown> {\n\t\tif (is(table, PgTable)) {\n\t\t\treturn table[Table.Symbol.Columns];\n\t\t} else if (is(table, Subquery)) {\n\t\t\treturn table._.selectedFields;\n\t\t}\n\t\treturn table[ViewBaseConfig].selectedFields;\n\t}\n\n\tprivate createJoin<TJoinType extends JoinType>(\n\t\tjoinType: TJoinType,\n\t): PgUpdateJoinFn<this, TDynamic, TJoinType> {\n\t\treturn ((\n\t\t\ttable: PgTable | Subquery | PgViewBase | SQL,\n\t\t\ton: ((updateTable: TTable, from: TFrom) => SQL | undefined) | SQL | undefined,\n\t\t) => {\n\t\t\tconst tableName = getTableLikeName(table);\n\n\t\t\tif (typeof tableName === 'string' && this.config.joins.some((join) => join.alias === tableName)) {\n\t\t\t\tthrow new Error(`Alias \"${tableName}\" is already used in this query`);\n\t\t\t}\n\n\t\t\tif (typeof on === 'function') {\n\t\t\t\tconst from = this.config.from && !is(this.config.from, SQL)\n\t\t\t\t\t? this.getTableLikeFields(this.config.from)\n\t\t\t\t\t: undefined;\n\t\t\t\ton = on(\n\t\t\t\t\tnew Proxy(\n\t\t\t\t\t\tthis.config.table[Table.Symbol.Columns],\n\t\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'sql', sqlBehavior: 'sql' }),\n\t\t\t\t\t) as any,\n\t\t\t\t\tfrom && new Proxy(\n\t\t\t\t\t\tfrom,\n\t\t\t\t\t\tnew SelectionProxyHandler({ sqlAliasedBehavior: 'sql', sqlBehavior: 'sql' }),\n\t\t\t\t\t) as any,\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tthis.config.joins.push({ on, table, joinType, alias: tableName });\n\n\t\t\tif (typeof tableName === 'string') {\n\t\t\t\tswitch (joinType) {\n\t\t\t\t\tcase 'left': {\n\t\t\t\t\t\tthis.joinsNotNullableMap[tableName] = false;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'right': {\n\t\t\t\t\t\tthis.joinsNotNullableMap = Object.fromEntries(\n\t\t\t\t\t\t\tObject.entries(this.joinsNotNullableMap).map(([key]) => [key, false]),\n\t\t\t\t\t\t);\n\t\t\t\t\t\tthis.joinsNotNullableMap[tableName] = true;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'inner': {\n\t\t\t\t\t\tthis.joinsNotNullableMap[tableName] = true;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tcase 'full': {\n\t\t\t\t\t\tthis.joinsNotNullableMap = Object.fromEntries(\n\t\t\t\t\t\t\tObject.entries(this.joinsNotNullableMap).map(([key]) => [key, false]),\n\t\t\t\t\t\t);\n\t\t\t\t\t\tthis.joinsNotNullableMap[tableName] = false;\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\treturn this as any;\n\t\t}) as any;\n\t}\n\n\tleftJoin = this.createJoin('left');\n\n\trightJoin = this.createJoin('right');\n\n\tinnerJoin = this.createJoin('inner');\n\n\tfullJoin = this.createJoin('full');\n\n\t/**\n\t * Adds a 'where' clause to the query.\n\t *\n\t * Calling this method will update only those rows that fulfill a specified condition.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/update}\n\t *\n\t * @param where the 'where' clause.\n\t *\n\t * @example\n\t * You can use conditional operators and `sql function` to filter the rows to be updated.\n\t *\n\t * ```ts\n\t * // Update all cars with green color\n\t * await db.update(cars).set({ color: 'red' })\n\t *   .where(eq(cars.color, 'green'));\n\t * // or\n\t * await db.update(cars).set({ color: 'red' })\n\t *   .where(sql`${cars.color} = 'green'`)\n\t * ```\n\t *\n\t * You can logically combine conditional operators with `and()` and `or()` operators:\n\t *\n\t * ```ts\n\t * // Update all BMW cars with a green color\n\t * await db.update(cars).set({ color: 'red' })\n\t *   .where(and(eq(cars.color, 'green'), eq(cars.brand, 'BMW')));\n\t *\n\t * // Update all cars with the green or blue color\n\t * await db.update(cars).set({ color: 'red' })\n\t *   .where(or(eq(cars.color, 'green'), eq(cars.color, 'blue')));\n\t * ```\n\t */\n\twhere(where: SQL | undefined): PgUpdateWithout<this, TDynamic, 'where'> {\n\t\tthis.config.where = where;\n\t\treturn this as any;\n\t}\n\n\t/**\n\t * Adds a `returning` clause to the query.\n\t *\n\t * Calling this method will return the specified fields of the updated rows. If no fields are specified, all fields will be returned.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/update#update-with-returning}\n\t *\n\t * @example\n\t * ```ts\n\t * // Update all cars with the green color and return all fields\n\t * const updatedCars: Car[] = await db.update(cars)\n\t *   .set({ color: 'red' })\n\t *   .where(eq(cars.color, 'green'))\n\t *   .returning();\n\t *\n\t * // Update all cars with the green color and return only their id and brand fields\n\t * const updatedCarsIdsAndBrands: { id: number, brand: string }[] = await db.update(cars)\n\t *   .set({ color: 'red' })\n\t *   .where(eq(cars.color, 'green'))\n\t *   .returning({ id: cars.id, brand: cars.brand });\n\t * ```\n\t */\n\treturning(): PgUpdateReturningAll<this, TDynamic>;\n\treturning<TSelectedFields extends SelectedFields>(\n\t\tfields: TSelectedFields,\n\t): PgUpdateReturning<this, TDynamic, TSelectedFields>;\n\treturning(\n\t\tfields?: SelectedFields,\n\t): PgUpdateWithout<AnyPgUpdate, TDynamic, 'returning'> {\n\t\tif (!fields) {\n\t\t\tfields = Object.assign({}, this.config.table[Table.Symbol.Columns]);\n\n\t\t\tif (this.config.from) {\n\t\t\t\tconst tableName = getTableLikeName(this.config.from);\n\n\t\t\t\tif (typeof tableName === 'string' && this.config.from && !is(this.config.from, SQL)) {\n\t\t\t\t\tconst fromFields = this.getTableLikeFields(this.config.from);\n\t\t\t\t\tfields[tableName] = fromFields as any;\n\t\t\t\t}\n\n\t\t\t\tfor (const join of this.config.joins) {\n\t\t\t\t\tconst tableName = getTableLikeName(join.table);\n\n\t\t\t\t\tif (typeof tableName === 'string' && !is(join.table, SQL)) {\n\t\t\t\t\t\tconst fromFields = this.getTableLikeFields(join.table);\n\t\t\t\t\t\tfields[tableName] = fromFields as any;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tthis.config.returningFields = fields;\n\t\tthis.config.returning = orderSelectedFields<PgColumn>(fields);\n\t\treturn this as any;\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this.dialect.buildUpdateQuery(this.config);\n\t}\n\n\ttoSQL(): Query {\n\t\tconst { typings: _typings, ...rest } = this.dialect.sqlToQuery(this.getSQL());\n\t\treturn rest;\n\t}\n\n\t/** @internal */\n\t_prepare(name?: string): PgUpdatePrepare<this> {\n\t\tconst query = this.session.prepareQuery<\n\t\t\tPreparedQueryConfig & { execute: TReturning[] }\n\t\t>(this.dialect.sqlToQuery(this.getSQL()), this.config.returning, name, true);\n\t\tquery.joinsNotNullableMap = this.joinsNotNullableMap;\n\t\treturn query;\n\t}\n\n\tprepare(name: string): PgUpdatePrepare<this> {\n\t\treturn this._prepare(name);\n\t}\n\n\tprivate authToken?: NeonAuthToken;\n\t/** @internal */\n\tsetToken(token?: NeonAuthToken) {\n\t\tthis.authToken = token;\n\t\treturn this;\n\t}\n\n\toverride execute: ReturnType<this['prepare']>['execute'] = (placeholderValues) => {\n\t\treturn this._prepare().execute(placeholderValues, this.authToken);\n\t};\n\n\t/** @internal */\n\tgetSelectedFields(): this['_']['selectedFields'] {\n\t\treturn (\n\t\t\tthis.config.returningFields\n\t\t\t\t? new Proxy(\n\t\t\t\t\tthis.config.returningFields,\n\t\t\t\t\tnew SelectionProxyHandler({\n\t\t\t\t\t\talias: getTableName(this.config.table),\n\t\t\t\t\t\tsqlAliasedBehavior: 'alias',\n\t\t\t\t\t\tsqlBehavior: 'error',\n\t\t\t\t\t}),\n\t\t\t\t)\n\t\t\t\t: undefined\n\t\t) as this['_']['selectedFields'];\n\t}\n\n\t$dynamic(): PgUpdateDynamic<this> {\n\t\treturn this as any;\n\t}\n}\n", "import { entityKind } from '~/entity.ts';\nimport { SQL, sql, type S<PERSON>Wrapper } from '~/sql/sql.ts';\nimport type { NeonAuthToken } from '~/utils.ts';\nimport type { PgSession } from '../session.ts';\nimport type { PgTable } from '../table.ts';\n\nexport class PgCountBuilder<\n\tTSession extends PgSession<any, any, any>,\n> extends SQL<number> implements Promise<number>, SQLWrapper {\n\tprivate sql: SQL<number>;\n\tprivate token?: NeonAuthToken;\n\n\tstatic override readonly [entityKind] = 'PgCountBuilder';\n\t[Symbol.toStringTag] = 'PgCountBuilder';\n\n\tprivate session: TSession;\n\n\tprivate static buildEmbeddedCount(\n\t\tsource: PgTable | SQL | SQLWrapper,\n\t\tfilters?: SQL<unknown>,\n\t): SQL<number> {\n\t\treturn sql<number>`(select count(*) from ${source}${sql.raw(' where ').if(filters)}${filters})`;\n\t}\n\n\tprivate static buildCount(\n\t\tsource: PgTable | SQL | SQLWrapper,\n\t\tfilters?: SQL<unknown>,\n\t): SQL<number> {\n\t\treturn sql<number>`select count(*) as count from ${source}${sql.raw(' where ').if(filters)}${filters};`;\n\t}\n\n\tconstructor(\n\t\treadonly params: {\n\t\t\tsource: PgTable | SQL | SQLWrapper;\n\t\t\tfilters?: SQL<unknown>;\n\t\t\tsession: TSession;\n\t\t},\n\t) {\n\t\tsuper(PgCountBuilder.buildEmbeddedCount(params.source, params.filters).queryChunks);\n\n\t\tthis.mapWith(Number);\n\n\t\tthis.session = params.session;\n\n\t\tthis.sql = PgCountBuilder.buildCount(\n\t\t\tparams.source,\n\t\t\tparams.filters,\n\t\t);\n\t}\n\n\t/** @intrnal */\n\tsetToken(token?: NeonAuthToken) {\n\t\tthis.token = token;\n\t\treturn this;\n\t}\n\n\tthen<TResult1 = number, TResult2 = never>(\n\t\tonfulfilled?: ((value: number) => TResult1 | PromiseLike<TResult1>) | null | undefined,\n\t\tonrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | null | undefined,\n\t): Promise<TResult1 | TResult2> {\n\t\treturn Promise.resolve(this.session.count(this.sql, this.token))\n\t\t\t.then(\n\t\t\t\tonfulfilled,\n\t\t\t\tonrejected,\n\t\t\t);\n\t}\n\n\tcatch(\n\t\tonRejected?: ((reason: any) => any) | null | undefined,\n\t): Promise<number> {\n\t\treturn this.then(undefined, onRejected);\n\t}\n\n\tfinally(onFinally?: (() => void) | null | undefined): Promise<number> {\n\t\treturn this.then(\n\t\t\t(value) => {\n\t\t\t\tonFinally?.();\n\t\t\t\treturn value;\n\t\t\t},\n\t\t\t(reason) => {\n\t\t\t\tonFinally?.();\n\t\t\t\tthrow reason;\n\t\t\t},\n\t\t);\n\t}\n}\n", "import { entityKind } from '~/entity.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport {\n\ttype BuildQueryResult,\n\ttype BuildRelationalQueryResult,\n\ttype DBQueryConfig,\n\tmapRelationalRow,\n\ttype TableRelationalConfig,\n\ttype TablesRelationalConfig,\n} from '~/relations.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport type { Query, QueryWithTypings, SQL, SQLWrapper } from '~/sql/sql.ts';\nimport { tracer } from '~/tracing.ts';\nimport type { KnownKeysOnly, NeonAuthToken } from '~/utils.ts';\nimport type { PgDialect } from '../dialect.ts';\nimport type { PgPreparedQuery, PgSession, PreparedQueryConfig } from '../session.ts';\nimport type { PgTable } from '../table.ts';\n\nexport class RelationalQueryBuilder<TSchema extends TablesRelationalConfig, TFields extends TableRelationalConfig> {\n\tstatic readonly [entityKind]: string = 'PgRelationalQueryBuilder';\n\n\tconstructor(\n\t\tprivate fullSchema: Record<string, unknown>,\n\t\tprivate schema: TSchema,\n\t\tprivate tableNamesMap: Record<string, string>,\n\t\tprivate table: PgTable,\n\t\tprivate tableConfig: TableRelationalConfig,\n\t\tprivate dialect: PgDialect,\n\t\tprivate session: PgSession,\n\t) {}\n\n\tfindMany<TConfig extends DBQueryConfig<'many', true, TSchema, TFields>>(\n\t\tconfig?: KnownKeysOnly<TConfig, DBQueryConfig<'many', true, TSchema, TFields>>,\n\t): PgRelationalQuery<BuildQueryResult<TSchema, TFields, TConfig>[]> {\n\t\treturn new PgRelationalQuery(\n\t\t\tthis.fullSchema,\n\t\t\tthis.schema,\n\t\t\tthis.tableNamesMap,\n\t\t\tthis.table,\n\t\t\tthis.tableConfig,\n\t\t\tthis.dialect,\n\t\t\tthis.session,\n\t\t\tconfig ? (config as DBQueryConfig<'many', true>) : {},\n\t\t\t'many',\n\t\t);\n\t}\n\n\tfindFirst<TSelection extends Omit<DBQueryConfig<'many', true, TSchema, TFields>, 'limit'>>(\n\t\tconfig?: KnownKeysOnly<TSelection, Omit<DBQueryConfig<'many', true, TSchema, TFields>, 'limit'>>,\n\t): PgRelationalQuery<BuildQueryResult<TSchema, TFields, TSelection> | undefined> {\n\t\treturn new PgRelationalQuery(\n\t\t\tthis.fullSchema,\n\t\t\tthis.schema,\n\t\t\tthis.tableNamesMap,\n\t\t\tthis.table,\n\t\t\tthis.tableConfig,\n\t\t\tthis.dialect,\n\t\t\tthis.session,\n\t\t\tconfig ? { ...(config as DBQueryConfig<'many', true> | undefined), limit: 1 } : { limit: 1 },\n\t\t\t'first',\n\t\t);\n\t}\n}\n\nexport class PgRelationalQuery<TResult> extends QueryPromise<TResult>\n\timplements RunnableQuery<TResult, 'pg'>, SQLWrapper\n{\n\tstatic override readonly [entityKind]: string = 'PgRelationalQuery';\n\n\tdeclare readonly _: {\n\t\treadonly dialect: 'pg';\n\t\treadonly result: TResult;\n\t};\n\n\tconstructor(\n\t\tprivate fullSchema: Record<string, unknown>,\n\t\tprivate schema: TablesRelationalConfig,\n\t\tprivate tableNamesMap: Record<string, string>,\n\t\tprivate table: PgTable,\n\t\tprivate tableConfig: TableRelationalConfig,\n\t\tprivate dialect: PgDialect,\n\t\tprivate session: PgSession,\n\t\tprivate config: DBQueryConfig<'many', true> | true,\n\t\tprivate mode: 'many' | 'first',\n\t) {\n\t\tsuper();\n\t}\n\n\t/** @internal */\n\t_prepare(name?: string): PgPreparedQuery<PreparedQueryConfig & { execute: TResult }> {\n\t\treturn tracer.startActiveSpan('drizzle.prepareQuery', () => {\n\t\t\tconst { query, builtQuery } = this._toSQL();\n\n\t\t\treturn this.session.prepareQuery<PreparedQueryConfig & { execute: TResult }>(\n\t\t\t\tbuiltQuery,\n\t\t\t\tundefined,\n\t\t\t\tname,\n\t\t\t\ttrue,\n\t\t\t\t(rawRows, mapColumnValue) => {\n\t\t\t\t\tconst rows = rawRows.map((row) =>\n\t\t\t\t\t\tmapRelationalRow(this.schema, this.tableConfig, row, query.selection, mapColumnValue)\n\t\t\t\t\t);\n\t\t\t\t\tif (this.mode === 'first') {\n\t\t\t\t\t\treturn rows[0] as TResult;\n\t\t\t\t\t}\n\t\t\t\t\treturn rows as TResult;\n\t\t\t\t},\n\t\t\t);\n\t\t});\n\t}\n\n\tprepare(name: string): PgPreparedQuery<PreparedQueryConfig & { execute: TResult }> {\n\t\treturn this._prepare(name);\n\t}\n\n\tprivate _getQuery() {\n\t\treturn this.dialect.buildRelationalQueryWithoutPK({\n\t\t\tfullSchema: this.fullSchema,\n\t\t\tschema: this.schema,\n\t\t\ttableNamesMap: this.tableNamesMap,\n\t\t\ttable: this.table,\n\t\t\ttableConfig: this.tableConfig,\n\t\t\tqueryConfig: this.config,\n\t\t\ttableAlias: this.tableConfig.tsName,\n\t\t});\n\t}\n\n\t/** @internal */\n\tgetSQL(): SQL {\n\t\treturn this._getQuery().sql as SQL;\n\t}\n\n\tprivate _toSQL(): { query: BuildRelationalQueryResult; builtQuery: QueryWithTypings } {\n\t\tconst query = this._getQuery();\n\n\t\tconst builtQuery = this.dialect.sqlToQuery(query.sql as SQL);\n\n\t\treturn { query, builtQuery };\n\t}\n\n\ttoSQL(): Query {\n\t\treturn this._toSQL().builtQuery;\n\t}\n\n\tprivate authToken?: NeonAuthToken;\n\t/** @internal */\n\tsetToken(token?: NeonAuthToken) {\n\t\tthis.authToken = token;\n\t\treturn this;\n\t}\n\n\toverride execute(): Promise<TResult> {\n\t\treturn tracer.startActiveSpan('drizzle.operation', () => {\n\t\t\treturn this._prepare().execute(undefined, this.authToken);\n\t\t});\n\t}\n}\n", "import { entityKind } from '~/entity.ts';\nimport { QueryPromise } from '~/query-promise.ts';\nimport type { RunnableQuery } from '~/runnable-query.ts';\nimport type { PreparedQuery } from '~/session.ts';\nimport type { Query, SQL, SQLWrapper } from '~/sql/sql.ts';\n\nexport interface PgRaw<TResult> extends QueryPromise<TResult>, RunnableQuery<TResult, 'pg'>, SQLWrapper {}\n\nexport class PgRaw<TResult> extends QueryPromise<TResult>\n\timplements RunnableQuery<TResult, 'pg'>, SQLWrapper, PreparedQuery\n{\n\tstatic override readonly [entityKind]: string = 'PgRaw';\n\n\tdeclare readonly _: {\n\t\treadonly dialect: 'pg';\n\t\treadonly result: TResult;\n\t};\n\n\tconstructor(\n\t\tpublic execute: () => Promise<TResult>,\n\t\tprivate sql: SQL,\n\t\tprivate query: Query,\n\t\tprivate mapBatchResult: (result: unknown) => unknown,\n\t) {\n\t\tsuper();\n\t}\n\n\t/** @internal */\n\tgetSQL() {\n\t\treturn this.sql;\n\t}\n\n\tgetQuery() {\n\t\treturn this.query;\n\t}\n\n\tmapResult(result: unknown, isFromBatch?: boolean) {\n\t\treturn isFromBatch ? this.mapBatchResult(result) : result;\n\t}\n\n\t_prepare(): PreparedQuery {\n\t\treturn this;\n\t}\n\n\t/** @internal */\n\tisResponseInArrayMode() {\n\t\treturn false;\n\t}\n}\n", "import { entityKind } from '~/entity.ts';\nimport type { PgDialect } from '~/pg-core/dialect.ts';\nimport {\n\tPgDeleteBase,\n\tPgInsertBuilder,\n\tPgSelectBuilder,\n\tPgUpdateBuilder,\n\tQueryBuilder,\n} from '~/pg-core/query-builders/index.ts';\nimport type {\n\tPgQueryResultHKT,\n\tPgQueryResultKind,\n\tPgSession,\n\tPgTransaction,\n\tPgTransactionConfig,\n\tPreparedQueryConfig,\n} from '~/pg-core/session.ts';\nimport type { PgTable } from '~/pg-core/table.ts';\nimport type { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport type { ExtractTablesWithRelations, RelationalSchemaConfig, TablesRelationalConfig } from '~/relations.ts';\nimport { SelectionProxyHandler } from '~/selection-proxy.ts';\nimport { type ColumnsSelection, type SQL, sql, type SQLWrapper } from '~/sql/sql.ts';\nimport { WithSubquery } from '~/subquery.ts';\nimport type { DrizzleTypeError, NeonAuthToken } from '~/utils.ts';\nimport type { PgColumn } from './columns/index.ts';\nimport { PgCountBuilder } from './query-builders/count.ts';\nimport { RelationalQueryBuilder } from './query-builders/query.ts';\nimport { PgRaw } from './query-builders/raw.ts';\nimport { PgRefreshMaterializedView } from './query-builders/refresh-materialized-view.ts';\nimport type { SelectedFields } from './query-builders/select.types.ts';\nimport type { WithBuilder } from './subquery.ts';\nimport type { PgViewBase } from './view-base.ts';\nimport type { PgMaterializedView } from './view.ts';\n\nexport class PgDatabase<\n\tTQueryResult extends PgQueryResultHKT,\n\tTFullSchema extends Record<string, unknown> = Record<string, never>,\n\tTSchema extends TablesRelationalConfig = ExtractTablesWithRelations<TFullSchema>,\n> {\n\tstatic readonly [entityKind]: string = 'PgDatabase';\n\n\tdeclare readonly _: {\n\t\treadonly schema: TSchema | undefined;\n\t\treadonly fullSchema: TFullSchema;\n\t\treadonly tableNamesMap: Record<string, string>;\n\t\treadonly session: PgSession<TQueryResult, TFullSchema, TSchema>;\n\t};\n\n\tquery: TFullSchema extends Record<string, never>\n\t\t? DrizzleTypeError<'Seems like the schema generic is missing - did you forget to add it to your DB type?'>\n\t\t: {\n\t\t\t[K in keyof TSchema]: RelationalQueryBuilder<TSchema, TSchema[K]>;\n\t\t};\n\n\tconstructor(\n\t\t/** @internal */\n\t\treadonly dialect: PgDialect,\n\t\t/** @internal */\n\t\treadonly session: PgSession<any, any, any>,\n\t\tschema: RelationalSchemaConfig<TSchema> | undefined,\n\t) {\n\t\tthis._ = schema\n\t\t\t? {\n\t\t\t\tschema: schema.schema,\n\t\t\t\tfullSchema: schema.fullSchema as TFullSchema,\n\t\t\t\ttableNamesMap: schema.tableNamesMap,\n\t\t\t\tsession,\n\t\t\t}\n\t\t\t: {\n\t\t\t\tschema: undefined,\n\t\t\t\tfullSchema: {} as TFullSchema,\n\t\t\t\ttableNamesMap: {},\n\t\t\t\tsession,\n\t\t\t};\n\t\tthis.query = {} as typeof this['query'];\n\t\tif (this._.schema) {\n\t\t\tfor (const [tableName, columns] of Object.entries(this._.schema)) {\n\t\t\t\t(this.query as PgDatabase<TQueryResult, Record<string, any>>['query'])[tableName] = new RelationalQueryBuilder(\n\t\t\t\t\tschema!.fullSchema,\n\t\t\t\t\tthis._.schema,\n\t\t\t\t\tthis._.tableNamesMap,\n\t\t\t\t\tschema!.fullSchema[tableName] as PgTable,\n\t\t\t\t\tcolumns,\n\t\t\t\t\tdialect,\n\t\t\t\t\tsession,\n\t\t\t\t);\n\t\t\t}\n\t\t}\n\t}\n\n\t/**\n\t * Creates a subquery that defines a temporary named result set as a CTE.\n\t *\n\t * It is useful for breaking down complex queries into simpler parts and for reusing the result set in subsequent parts of the query.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#with-clause}\n\t *\n\t * @param alias The alias for the subquery.\n\t *\n\t * Failure to provide an alias will result in a DrizzleTypeError, preventing the subquery from being referenced in other queries.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Create a subquery with alias 'sq' and use it in the select query\n\t * const sq = db.$with('sq').as(db.select().from(users).where(eq(users.id, 42)));\n\t *\n\t * const result = await db.with(sq).select().from(sq);\n\t * ```\n\t *\n\t * To select arbitrary SQL values as fields in a CTE and reference them in other CTEs or in the main query, you need to add aliases to them:\n\t *\n\t * ```ts\n\t * // Select an arbitrary SQL value as a field in a CTE and reference it in the main query\n\t * const sq = db.$with('sq').as(db.select({\n\t *   name: sql<string>`upper(${users.name})`.as('name'),\n\t * })\n\t * .from(users));\n\t *\n\t * const result = await db.with(sq).select({ name: sq.name }).from(sq);\n\t * ```\n\t */\n\t$with: WithBuilder = (alias: string, selection?: ColumnsSelection) => {\n\t\tconst self = this;\n\t\tconst as = (\n\t\t\tqb:\n\t\t\t\t| TypedQueryBuilder<ColumnsSelection | undefined>\n\t\t\t\t| SQL\n\t\t\t\t| ((qb: QueryBuilder) => TypedQueryBuilder<ColumnsSelection | undefined> | SQL),\n\t\t) => {\n\t\t\tif (typeof qb === 'function') {\n\t\t\t\tqb = qb(new QueryBuilder(self.dialect));\n\t\t\t}\n\n\t\t\treturn new Proxy(\n\t\t\t\tnew WithSubquery(\n\t\t\t\t\tqb.getSQL(),\n\t\t\t\t\tselection ?? ('getSelectedFields' in qb ? qb.getSelectedFields() ?? {} : {}) as SelectedFields,\n\t\t\t\t\talias,\n\t\t\t\t\ttrue,\n\t\t\t\t),\n\t\t\t\tnew SelectionProxyHandler({ alias, sqlAliasedBehavior: 'alias', sqlBehavior: 'error' }),\n\t\t\t);\n\t\t};\n\t\treturn { as };\n\t};\n\n\t$count(\n\t\tsource: PgTable | PgViewBase | SQL | SQLWrapper,\n\t\tfilters?: SQL<unknown>,\n\t) {\n\t\treturn new PgCountBuilder({ source, filters, session: this.session });\n\t}\n\n\t/**\n\t * Incorporates a previously defined CTE (using `$with`) into the main query.\n\t *\n\t * This method allows the main query to reference a temporary named result set.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#with-clause}\n\t *\n\t * @param queries The CTEs to incorporate into the main query.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Define a subquery 'sq' as a CTE using $with\n\t * const sq = db.$with('sq').as(db.select().from(users).where(eq(users.id, 42)));\n\t *\n\t * // Incorporate the CTE 'sq' into the main query and select from it\n\t * const result = await db.with(sq).select().from(sq);\n\t * ```\n\t */\n\twith(...queries: WithSubquery[]) {\n\t\tconst self = this;\n\n\t\t/**\n\t\t * Creates a select query.\n\t\t *\n\t\t * Calling this method with no arguments will select all columns from the table. Pass a selection object to specify the columns you want to select.\n\t\t *\n\t\t * Use `.from()` method to specify which table to select from.\n\t\t *\n\t\t * See docs: {@link https://orm.drizzle.team/docs/select}\n\t\t *\n\t\t * @param fields The selection object.\n\t\t *\n\t\t * @example\n\t\t *\n\t\t * ```ts\n\t\t * // Select all columns and all rows from the 'cars' table\n\t\t * const allCars: Car[] = await db.select().from(cars);\n\t\t *\n\t\t * // Select specific columns and all rows from the 'cars' table\n\t\t * const carsIdsAndBrands: { id: number; brand: string }[] = await db.select({\n\t\t *   id: cars.id,\n\t\t *   brand: cars.brand\n\t\t * })\n\t\t *   .from(cars);\n\t\t * ```\n\t\t *\n\t\t * Like in SQL, you can use arbitrary expressions as selection fields, not just table columns:\n\t\t *\n\t\t * ```ts\n\t\t * // Select specific columns along with expression and all rows from the 'cars' table\n\t\t * const carsIdsAndLowerNames: { id: number; lowerBrand: string }[] = await db.select({\n\t\t *   id: cars.id,\n\t\t *   lowerBrand: sql<string>`lower(${cars.brand})`,\n\t\t * })\n\t\t *   .from(cars);\n\t\t * ```\n\t\t */\n\t\tfunction select(): PgSelectBuilder<undefined>;\n\t\tfunction select<TSelection extends SelectedFields>(fields: TSelection): PgSelectBuilder<TSelection>;\n\t\tfunction select<TSelection extends SelectedFields>(fields?: TSelection): PgSelectBuilder<TSelection | undefined> {\n\t\t\treturn new PgSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: self.session,\n\t\t\t\tdialect: self.dialect,\n\t\t\t\twithList: queries,\n\t\t\t});\n\t\t}\n\n\t\t/**\n\t\t * Adds `distinct` expression to the select query.\n\t\t *\n\t\t * Calling this method will return only unique values. When multiple columns are selected, it returns rows with unique combinations of values in these columns.\n\t\t *\n\t\t * Use `.from()` method to specify which table to select from.\n\t\t *\n\t\t * See docs: {@link https://orm.drizzle.team/docs/select#distinct}\n\t\t *\n\t\t * @param fields The selection object.\n\t\t *\n\t\t * @example\n\t\t * ```ts\n\t\t * // Select all unique rows from the 'cars' table\n\t\t * await db.selectDistinct()\n\t\t *   .from(cars)\n\t\t *   .orderBy(cars.id, cars.brand, cars.color);\n\t\t *\n\t\t * // Select all unique brands from the 'cars' table\n\t\t * await db.selectDistinct({ brand: cars.brand })\n\t\t *   .from(cars)\n\t\t *   .orderBy(cars.brand);\n\t\t * ```\n\t\t */\n\t\tfunction selectDistinct(): PgSelectBuilder<undefined>;\n\t\tfunction selectDistinct<TSelection extends SelectedFields>(fields: TSelection): PgSelectBuilder<TSelection>;\n\t\tfunction selectDistinct<TSelection extends SelectedFields>(\n\t\t\tfields?: TSelection,\n\t\t): PgSelectBuilder<TSelection | undefined> {\n\t\t\treturn new PgSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: self.session,\n\t\t\t\tdialect: self.dialect,\n\t\t\t\twithList: queries,\n\t\t\t\tdistinct: true,\n\t\t\t});\n\t\t}\n\n\t\t/**\n\t\t * Adds `distinct on` expression to the select query.\n\t\t *\n\t\t * Calling this method will specify how the unique rows are determined.\n\t\t *\n\t\t * Use `.from()` method to specify which table to select from.\n\t\t *\n\t\t * See docs: {@link https://orm.drizzle.team/docs/select#distinct}\n\t\t *\n\t\t * @param on The expression defining uniqueness.\n\t\t * @param fields The selection object.\n\t\t *\n\t\t * @example\n\t\t * ```ts\n\t\t * // Select the first row for each unique brand from the 'cars' table\n\t\t * await db.selectDistinctOn([cars.brand])\n\t\t *   .from(cars)\n\t\t *   .orderBy(cars.brand);\n\t\t *\n\t\t * // Selects the first occurrence of each unique car brand along with its color from the 'cars' table\n\t\t * await db.selectDistinctOn([cars.brand], { brand: cars.brand, color: cars.color })\n\t\t *   .from(cars)\n\t\t *   .orderBy(cars.brand, cars.color);\n\t\t * ```\n\t\t */\n\t\tfunction selectDistinctOn(on: (PgColumn | SQLWrapper)[]): PgSelectBuilder<undefined>;\n\t\tfunction selectDistinctOn<TSelection extends SelectedFields>(\n\t\t\ton: (PgColumn | SQLWrapper)[],\n\t\t\tfields: TSelection,\n\t\t): PgSelectBuilder<TSelection>;\n\t\tfunction selectDistinctOn<TSelection extends SelectedFields>(\n\t\t\ton: (PgColumn | SQLWrapper)[],\n\t\t\tfields?: TSelection,\n\t\t): PgSelectBuilder<TSelection | undefined> {\n\t\t\treturn new PgSelectBuilder({\n\t\t\t\tfields: fields ?? undefined,\n\t\t\t\tsession: self.session,\n\t\t\t\tdialect: self.dialect,\n\t\t\t\twithList: queries,\n\t\t\t\tdistinct: { on },\n\t\t\t});\n\t\t}\n\n\t\t/**\n\t\t * Creates an update query.\n\t\t *\n\t\t * Calling this method without `.where()` clause will update all rows in a table. The `.where()` clause specifies which rows should be updated.\n\t\t *\n\t\t * Use `.set()` method to specify which values to update.\n\t\t *\n\t\t * See docs: {@link https://orm.drizzle.team/docs/update}\n\t\t *\n\t\t * @param table The table to update.\n\t\t *\n\t\t * @example\n\t\t *\n\t\t * ```ts\n\t\t * // Update all rows in the 'cars' table\n\t\t * await db.update(cars).set({ color: 'red' });\n\t\t *\n\t\t * // Update rows with filters and conditions\n\t\t * await db.update(cars).set({ color: 'red' }).where(eq(cars.brand, 'BMW'));\n\t\t *\n\t\t * // Update with returning clause\n\t\t * const updatedCar: Car[] = await db.update(cars)\n\t\t *   .set({ color: 'red' })\n\t\t *   .where(eq(cars.id, 1))\n\t\t *   .returning();\n\t\t * ```\n\t\t */\n\t\tfunction update<TTable extends PgTable>(table: TTable): PgUpdateBuilder<TTable, TQueryResult> {\n\t\t\treturn new PgUpdateBuilder(table, self.session, self.dialect, queries);\n\t\t}\n\n\t\t/**\n\t\t * Creates an insert query.\n\t\t *\n\t\t * Calling this method will create new rows in a table. Use `.values()` method to specify which values to insert.\n\t\t *\n\t\t * See docs: {@link https://orm.drizzle.team/docs/insert}\n\t\t *\n\t\t * @param table The table to insert into.\n\t\t *\n\t\t * @example\n\t\t *\n\t\t * ```ts\n\t\t * // Insert one row\n\t\t * await db.insert(cars).values({ brand: 'BMW' });\n\t\t *\n\t\t * // Insert multiple rows\n\t\t * await db.insert(cars).values([{ brand: 'BMW' }, { brand: 'Porsche' }]);\n\t\t *\n\t\t * // Insert with returning clause\n\t\t * const insertedCar: Car[] = await db.insert(cars)\n\t\t *   .values({ brand: 'BMW' })\n\t\t *   .returning();\n\t\t * ```\n\t\t */\n\t\tfunction insert<TTable extends PgTable>(table: TTable): PgInsertBuilder<TTable, TQueryResult> {\n\t\t\treturn new PgInsertBuilder(table, self.session, self.dialect, queries);\n\t\t}\n\n\t\t/**\n\t\t * Creates a delete query.\n\t\t *\n\t\t * Calling this method without `.where()` clause will delete all rows in a table. The `.where()` clause specifies which rows should be deleted.\n\t\t *\n\t\t * See docs: {@link https://orm.drizzle.team/docs/delete}\n\t\t *\n\t\t * @param table The table to delete from.\n\t\t *\n\t\t * @example\n\t\t *\n\t\t * ```ts\n\t\t * // Delete all rows in the 'cars' table\n\t\t * await db.delete(cars);\n\t\t *\n\t\t * // Delete rows with filters and conditions\n\t\t * await db.delete(cars).where(eq(cars.color, 'green'));\n\t\t *\n\t\t * // Delete with returning clause\n\t\t * const deletedCar: Car[] = await db.delete(cars)\n\t\t *   .where(eq(cars.id, 1))\n\t\t *   .returning();\n\t\t * ```\n\t\t */\n\t\tfunction delete_<TTable extends PgTable>(table: TTable): PgDeleteBase<TTable, TQueryResult> {\n\t\t\treturn new PgDeleteBase(table, self.session, self.dialect, queries);\n\t\t}\n\n\t\treturn { select, selectDistinct, selectDistinctOn, update, insert, delete: delete_ };\n\t}\n\n\t/**\n\t * Creates a select query.\n\t *\n\t * Calling this method with no arguments will select all columns from the table. Pass a selection object to specify the columns you want to select.\n\t *\n\t * Use `.from()` method to specify which table to select from.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select}\n\t *\n\t * @param fields The selection object.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Select all columns and all rows from the 'cars' table\n\t * const allCars: Car[] = await db.select().from(cars);\n\t *\n\t * // Select specific columns and all rows from the 'cars' table\n\t * const carsIdsAndBrands: { id: number; brand: string }[] = await db.select({\n\t *   id: cars.id,\n\t *   brand: cars.brand\n\t * })\n\t *   .from(cars);\n\t * ```\n\t *\n\t * Like in SQL, you can use arbitrary expressions as selection fields, not just table columns:\n\t *\n\t * ```ts\n\t * // Select specific columns along with expression and all rows from the 'cars' table\n\t * const carsIdsAndLowerNames: { id: number; lowerBrand: string }[] = await db.select({\n\t *   id: cars.id,\n\t *   lowerBrand: sql<string>`lower(${cars.brand})`,\n\t * })\n\t *   .from(cars);\n\t * ```\n\t */\n\tselect(): PgSelectBuilder<undefined>;\n\tselect<TSelection extends SelectedFields>(fields: TSelection): PgSelectBuilder<TSelection>;\n\tselect<TSelection extends SelectedFields>(fields?: TSelection): PgSelectBuilder<TSelection | undefined> {\n\t\treturn new PgSelectBuilder({\n\t\t\tfields: fields ?? undefined,\n\t\t\tsession: this.session,\n\t\t\tdialect: this.dialect,\n\t\t});\n\t}\n\n\t/**\n\t * Adds `distinct` expression to the select query.\n\t *\n\t * Calling this method will return only unique values. When multiple columns are selected, it returns rows with unique combinations of values in these columns.\n\t *\n\t * Use `.from()` method to specify which table to select from.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#distinct}\n\t *\n\t * @param fields The selection object.\n\t *\n\t * @example\n\t * ```ts\n\t * // Select all unique rows from the 'cars' table\n\t * await db.selectDistinct()\n\t *   .from(cars)\n\t *   .orderBy(cars.id, cars.brand, cars.color);\n\t *\n\t * // Select all unique brands from the 'cars' table\n\t * await db.selectDistinct({ brand: cars.brand })\n\t *   .from(cars)\n\t *   .orderBy(cars.brand);\n\t * ```\n\t */\n\tselectDistinct(): PgSelectBuilder<undefined>;\n\tselectDistinct<TSelection extends SelectedFields>(fields: TSelection): PgSelectBuilder<TSelection>;\n\tselectDistinct<TSelection extends SelectedFields>(fields?: TSelection): PgSelectBuilder<TSelection | undefined> {\n\t\treturn new PgSelectBuilder({\n\t\t\tfields: fields ?? undefined,\n\t\t\tsession: this.session,\n\t\t\tdialect: this.dialect,\n\t\t\tdistinct: true,\n\t\t});\n\t}\n\n\t/**\n\t * Adds `distinct on` expression to the select query.\n\t *\n\t * Calling this method will specify how the unique rows are determined.\n\t *\n\t * Use `.from()` method to specify which table to select from.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/select#distinct}\n\t *\n\t * @param on The expression defining uniqueness.\n\t * @param fields The selection object.\n\t *\n\t * @example\n\t * ```ts\n\t * // Select the first row for each unique brand from the 'cars' table\n\t * await db.selectDistinctOn([cars.brand])\n\t *   .from(cars)\n\t *   .orderBy(cars.brand);\n\t *\n\t * // Selects the first occurrence of each unique car brand along with its color from the 'cars' table\n\t * await db.selectDistinctOn([cars.brand], { brand: cars.brand, color: cars.color })\n\t *   .from(cars)\n\t *   .orderBy(cars.brand, cars.color);\n\t * ```\n\t */\n\tselectDistinctOn(on: (PgColumn | SQLWrapper)[]): PgSelectBuilder<undefined>;\n\tselectDistinctOn<TSelection extends SelectedFields>(\n\t\ton: (PgColumn | SQLWrapper)[],\n\t\tfields: TSelection,\n\t): PgSelectBuilder<TSelection>;\n\tselectDistinctOn<TSelection extends SelectedFields>(\n\t\ton: (PgColumn | SQLWrapper)[],\n\t\tfields?: TSelection,\n\t): PgSelectBuilder<TSelection | undefined> {\n\t\treturn new PgSelectBuilder({\n\t\t\tfields: fields ?? undefined,\n\t\t\tsession: this.session,\n\t\t\tdialect: this.dialect,\n\t\t\tdistinct: { on },\n\t\t});\n\t}\n\n\t/**\n\t * Creates an update query.\n\t *\n\t * Calling this method without `.where()` clause will update all rows in a table. The `.where()` clause specifies which rows should be updated.\n\t *\n\t * Use `.set()` method to specify which values to update.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/update}\n\t *\n\t * @param table The table to update.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Update all rows in the 'cars' table\n\t * await db.update(cars).set({ color: 'red' });\n\t *\n\t * // Update rows with filters and conditions\n\t * await db.update(cars).set({ color: 'red' }).where(eq(cars.brand, 'BMW'));\n\t *\n\t * // Update with returning clause\n\t * const updatedCar: Car[] = await db.update(cars)\n\t *   .set({ color: 'red' })\n\t *   .where(eq(cars.id, 1))\n\t *   .returning();\n\t * ```\n\t */\n\tupdate<TTable extends PgTable>(table: TTable): PgUpdateBuilder<TTable, TQueryResult> {\n\t\treturn new PgUpdateBuilder(table, this.session, this.dialect);\n\t}\n\n\t/**\n\t * Creates an insert query.\n\t *\n\t * Calling this method will create new rows in a table. Use `.values()` method to specify which values to insert.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/insert}\n\t *\n\t * @param table The table to insert into.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Insert one row\n\t * await db.insert(cars).values({ brand: 'BMW' });\n\t *\n\t * // Insert multiple rows\n\t * await db.insert(cars).values([{ brand: 'BMW' }, { brand: 'Porsche' }]);\n\t *\n\t * // Insert with returning clause\n\t * const insertedCar: Car[] = await db.insert(cars)\n\t *   .values({ brand: 'BMW' })\n\t *   .returning();\n\t * ```\n\t */\n\tinsert<TTable extends PgTable>(table: TTable): PgInsertBuilder<TTable, TQueryResult> {\n\t\treturn new PgInsertBuilder(table, this.session, this.dialect);\n\t}\n\n\t/**\n\t * Creates a delete query.\n\t *\n\t * Calling this method without `.where()` clause will delete all rows in a table. The `.where()` clause specifies which rows should be deleted.\n\t *\n\t * See docs: {@link https://orm.drizzle.team/docs/delete}\n\t *\n\t * @param table The table to delete from.\n\t *\n\t * @example\n\t *\n\t * ```ts\n\t * // Delete all rows in the 'cars' table\n\t * await db.delete(cars);\n\t *\n\t * // Delete rows with filters and conditions\n\t * await db.delete(cars).where(eq(cars.color, 'green'));\n\t *\n\t * // Delete with returning clause\n\t * const deletedCar: Car[] = await db.delete(cars)\n\t *   .where(eq(cars.id, 1))\n\t *   .returning();\n\t * ```\n\t */\n\tdelete<TTable extends PgTable>(table: TTable): PgDeleteBase<TTable, TQueryResult> {\n\t\treturn new PgDeleteBase(table, this.session, this.dialect);\n\t}\n\n\trefreshMaterializedView<TView extends PgMaterializedView>(view: TView): PgRefreshMaterializedView<TQueryResult> {\n\t\treturn new PgRefreshMaterializedView(view, this.session, this.dialect);\n\t}\n\n\tprotected authToken?: NeonAuthToken;\n\n\texecute<TRow extends Record<string, unknown> = Record<string, unknown>>(\n\t\tquery: SQLWrapper | string,\n\t): PgRaw<PgQueryResultKind<TQueryResult, TRow>> {\n\t\tconst sequel = typeof query === 'string' ? sql.raw(query) : query.getSQL();\n\t\tconst builtQuery = this.dialect.sqlToQuery(sequel);\n\t\tconst prepared = this.session.prepareQuery<\n\t\t\tPreparedQueryConfig & { execute: PgQueryResultKind<TQueryResult, TRow> }\n\t\t>(\n\t\t\tbuiltQuery,\n\t\t\tundefined,\n\t\t\tundefined,\n\t\t\tfalse,\n\t\t);\n\t\treturn new PgRaw(\n\t\t\t() => prepared.execute(undefined, this.authToken),\n\t\t\tsequel,\n\t\t\tbuiltQuery,\n\t\t\t(result) => prepared.mapResult(result, true),\n\t\t);\n\t}\n\n\ttransaction<T>(\n\t\ttransaction: (tx: PgTransaction<TQueryResult, TFullSchema, TSchema>) => Promise<T>,\n\t\tconfig?: PgTransactionConfig,\n\t): Promise<T> {\n\t\treturn this.session.transaction(transaction, config);\n\t}\n}\n\nexport type PgWithReplicas<Q> = Q & { $primary: Q };\n\nexport const withReplicas = <\n\tHKT extends PgQueryResultHKT,\n\tTFullSchema extends Record<string, unknown>,\n\tTSchema extends TablesRelationalConfig,\n\tQ extends PgDatabase<\n\t\tHKT,\n\t\tTFullSchema,\n\t\tTSchema extends Record<string, unknown> ? ExtractTablesWithRelations<TFullSchema> : TSchema\n\t>,\n>(\n\tprimary: Q,\n\treplicas: [Q, ...Q[]],\n\tgetReplica: (replicas: Q[]) => Q = () => replicas[Math.floor(Math.random() * replicas.length)]!,\n): PgWithReplicas<Q> => {\n\tconst select: Q['select'] = (...args: []) => getReplica(replicas).select(...args);\n\tconst selectDistinct: Q['selectDistinct'] = (...args: []) => getReplica(replicas).selectDistinct(...args);\n\tconst selectDistinctOn: Q['selectDistinctOn'] = (...args: [any]) => getReplica(replicas).selectDistinctOn(...args);\n\tconst $count: Q['$count'] = (...args: [any]) => getReplica(replicas).$count(...args);\n\tconst _with: Q['with'] = (...args: any) => getReplica(replicas).with(...args);\n\tconst $with: Q['$with'] = (arg: any) => getReplica(replicas).$with(arg) as any;\n\n\tconst update: Q['update'] = (...args: [any]) => primary.update(...args);\n\tconst insert: Q['insert'] = (...args: [any]) => primary.insert(...args);\n\tconst $delete: Q['delete'] = (...args: [any]) => primary.delete(...args);\n\tconst execute: Q['execute'] = (...args: [any]) => primary.execute(...args);\n\tconst transaction: Q['transaction'] = (...args: [any]) => primary.transaction(...args);\n\tconst refreshMaterializedView: Q['refreshMaterializedView'] = (...args: [any]) =>\n\t\tprimary.refreshMaterializedView(...args);\n\n\treturn {\n\t\t...primary,\n\t\tupdate,\n\t\tinsert,\n\t\tdelete: $delete,\n\t\texecute,\n\t\ttransaction,\n\t\trefreshMaterializedView,\n\t\t$primary: primary,\n\t\tselect,\n\t\tselectDistinct,\n\t\tselectDistinctOn,\n\t\t$count,\n\t\t$with,\n\t\twith: _with,\n\t\tget query() {\n\t\t\treturn getReplica(replicas).query;\n\t\t},\n\t};\n};\n", "import { SQL } from '~/sql/sql.ts';\n\nimport { entityKind, is } from '~/entity.ts';\nimport type { ExtraConfigColumn, PgColumn } from './columns/index.ts';\nimport { IndexedColumn } from './columns/index.ts';\nimport type { PgTable } from './table.ts';\n\ninterface IndexConfig {\n\tname?: string;\n\n\tcolumns: Partial<IndexedColumn | SQL>[];\n\n\t/**\n\t * If true, the index will be created as `create unique index` instead of `create index`.\n\t */\n\tunique: boolean;\n\n\t/**\n\t * If true, the index will be created as `create index concurrently` instead of `create index`.\n\t */\n\tconcurrently?: boolean;\n\n\t/**\n\t * If true, the index will be created as `create index ... on only <table>` instead of `create index ... on <table>`.\n\t */\n\tonly: boolean;\n\n\t/**\n\t * Condition for partial index.\n\t */\n\twhere?: SQL;\n\n\t/**\n\t * The optional WITH clause specifies storage parameters for the index\n\t */\n\twith?: Record<string, any>;\n\n\t/**\n\t * The optional WITH clause method for the index\n\t */\n\tmethod?: 'btree' | string;\n}\n\nexport type IndexColumn = PgColumn;\n\nexport type PgIndexMethod = 'btree' | 'hash' | 'gist' | 'spgist' | 'gin' | 'brin' | 'hnsw' | 'ivfflat' | (string & {});\n\nexport type PgIndexOpClass =\n\t| 'abstime_ops'\n\t| 'access_method'\n\t| 'anyarray_eq'\n\t| 'anyarray_ge'\n\t| 'anyarray_gt'\n\t| 'anyarray_le'\n\t| 'anyarray_lt'\n\t| 'anyarray_ne'\n\t| 'bigint_ops'\n\t| 'bit_ops'\n\t| 'bool_ops'\n\t| 'box_ops'\n\t| 'bpchar_ops'\n\t| 'char_ops'\n\t| 'cidr_ops'\n\t| 'cstring_ops'\n\t| 'date_ops'\n\t| 'float_ops'\n\t| 'int2_ops'\n\t| 'int4_ops'\n\t| 'int8_ops'\n\t| 'interval_ops'\n\t| 'jsonb_ops'\n\t| 'macaddr_ops'\n\t| 'name_ops'\n\t| 'numeric_ops'\n\t| 'oid_ops'\n\t| 'oidint4_ops'\n\t| 'oidint8_ops'\n\t| 'oidname_ops'\n\t| 'oidvector_ops'\n\t| 'point_ops'\n\t| 'polygon_ops'\n\t| 'range_ops'\n\t| 'record_eq'\n\t| 'record_ge'\n\t| 'record_gt'\n\t| 'record_le'\n\t| 'record_lt'\n\t| 'record_ne'\n\t| 'text_ops'\n\t| 'time_ops'\n\t| 'timestamp_ops'\n\t| 'timestamptz_ops'\n\t| 'timetz_ops'\n\t| 'uuid_ops'\n\t| 'varbit_ops'\n\t| 'varchar_ops'\n\t// pg_vector types\n\t| 'xml_ops'\n\t| 'vector_l2_ops'\n\t| 'vector_ip_ops'\n\t| 'vector_cosine_ops'\n\t| 'vector_l1_ops'\n\t| 'bit_hamming_ops'\n\t| 'bit_jaccard_ops'\n\t| 'halfvec_l2_ops'\n\t| 'sparsevec_l2_op'\n\t| (string & {});\n\nexport class IndexBuilderOn {\n\tstatic readonly [entityKind]: string = 'PgIndexBuilderOn';\n\n\tconstructor(private unique: boolean, private name?: string) {}\n\n\ton(...columns: [Partial<ExtraConfigColumn> | SQL, ...Partial<ExtraConfigColumn | SQL>[]]): IndexBuilder {\n\t\treturn new IndexBuilder(\n\t\t\tcolumns.map((it) => {\n\t\t\t\tif (is(it, SQL)) {\n\t\t\t\t\treturn it;\n\t\t\t\t}\n\t\t\t\tit = it as ExtraConfigColumn;\n\t\t\t\tconst clonedIndexedColumn = new IndexedColumn(it.name, !!it.keyAsName, it.columnType!, it.indexConfig!);\n\t\t\t\tit.indexConfig = JSON.parse(JSON.stringify(it.defaultConfig));\n\t\t\t\treturn clonedIndexedColumn;\n\t\t\t}),\n\t\t\tthis.unique,\n\t\t\tfalse,\n\t\t\tthis.name,\n\t\t);\n\t}\n\n\tonOnly(...columns: [Partial<ExtraConfigColumn | SQL>, ...Partial<ExtraConfigColumn | SQL>[]]): IndexBuilder {\n\t\treturn new IndexBuilder(\n\t\t\tcolumns.map((it) => {\n\t\t\t\tif (is(it, SQL)) {\n\t\t\t\t\treturn it;\n\t\t\t\t}\n\t\t\t\tit = it as ExtraConfigColumn;\n\t\t\t\tconst clonedIndexedColumn = new IndexedColumn(it.name, !!it.keyAsName, it.columnType!, it.indexConfig!);\n\t\t\t\tit.indexConfig = it.defaultConfig;\n\t\t\t\treturn clonedIndexedColumn;\n\t\t\t}),\n\t\t\tthis.unique,\n\t\t\ttrue,\n\t\t\tthis.name,\n\t\t);\n\t}\n\n\t/**\n\t * Specify what index method to use. Choices are `btree`, `hash`, `gist`, `spgist`, `gin`, `brin`, or user-installed access methods like `bloom`. The default method is `btree.\n\t *\n\t * If you have the `pg_vector` extension installed in your database, you can use the `hnsw` and `ivfflat` options, which are predefined types.\n\t *\n\t * **You can always specify any string you want in the method, in case Drizzle doesn't have it natively in its types**\n\t *\n\t * @param method The name of the index method to be used\n\t * @param columns\n\t * @returns\n\t */\n\tusing(\n\t\tmethod: PgIndexMethod,\n\t\t...columns: [Partial<ExtraConfigColumn | SQL>, ...Partial<ExtraConfigColumn | SQL>[]]\n\t): IndexBuilder {\n\t\treturn new IndexBuilder(\n\t\t\tcolumns.map((it) => {\n\t\t\t\tif (is(it, SQL)) {\n\t\t\t\t\treturn it;\n\t\t\t\t}\n\t\t\t\tit = it as ExtraConfigColumn;\n\t\t\t\tconst clonedIndexedColumn = new IndexedColumn(it.name, !!it.keyAsName, it.columnType!, it.indexConfig!);\n\t\t\t\tit.indexConfig = JSON.parse(JSON.stringify(it.defaultConfig));\n\t\t\t\treturn clonedIndexedColumn;\n\t\t\t}),\n\t\t\tthis.unique,\n\t\t\ttrue,\n\t\t\tthis.name,\n\t\t\tmethod,\n\t\t);\n\t}\n}\n\nexport interface AnyIndexBuilder {\n\tbuild(table: PgTable): Index;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-empty-interface\nexport interface IndexBuilder extends AnyIndexBuilder {}\n\nexport class IndexBuilder implements AnyIndexBuilder {\n\tstatic readonly [entityKind]: string = 'PgIndexBuilder';\n\n\t/** @internal */\n\tconfig: IndexConfig;\n\n\tconstructor(\n\t\tcolumns: Partial<IndexedColumn | SQL>[],\n\t\tunique: boolean,\n\t\tonly: boolean,\n\t\tname?: string,\n\t\tmethod: string = 'btree',\n\t) {\n\t\tthis.config = {\n\t\t\tname,\n\t\t\tcolumns,\n\t\t\tunique,\n\t\t\tonly,\n\t\t\tmethod,\n\t\t};\n\t}\n\n\tconcurrently(): this {\n\t\tthis.config.concurrently = true;\n\t\treturn this;\n\t}\n\n\twith(obj: Record<string, any>): this {\n\t\tthis.config.with = obj;\n\t\treturn this;\n\t}\n\n\twhere(condition: SQL): this {\n\t\tthis.config.where = condition;\n\t\treturn this;\n\t}\n\n\t/** @internal */\n\tbuild(table: PgTable): Index {\n\t\treturn new Index(this.config, table);\n\t}\n}\n\nexport class Index {\n\tstatic readonly [entityKind]: string = 'PgIndex';\n\n\treadonly config: IndexConfig & { table: PgTable };\n\n\tconstructor(config: IndexConfig, table: PgTable) {\n\t\tthis.config = { ...config, table };\n\t}\n}\n\nexport type GetColumnsTableName<TColumns> = TColumns extends PgColumn ? TColumns['_']['name']\n\t: TColumns extends PgColumn[] ? TColumns[number]['_']['name']\n\t: never;\n\nexport function index(name?: string): IndexBuilderOn {\n\treturn new IndexBuilderOn(false, name);\n}\n\nexport function uniqueIndex(name?: string): IndexBuilderOn {\n\treturn new IndexBuilderOn(true, name);\n}\n", "import { entityKind } from '~/entity.ts';\nimport type { SQL } from '~/sql/sql.ts';\nimport type { PgRole } from './roles.ts';\nimport type { PgTable } from './table.ts';\n\nexport type PgPolicyToOption =\n\t| 'public'\n\t| 'current_role'\n\t| 'current_user'\n\t| 'session_user'\n\t| (string & {})\n\t| PgPolicyToOption[]\n\t| PgRole;\n\nexport interface PgPolicyConfig {\n\tas?: 'permissive' | 'restrictive';\n\tfor?: 'all' | 'select' | 'insert' | 'update' | 'delete';\n\tto?: PgPolicyToOption;\n\tusing?: SQL;\n\twithCheck?: SQL;\n}\n\nexport class PgPolicy implements PgPolicyConfig {\n\tstatic readonly [entityKind]: string = 'PgPolicy';\n\n\treadonly as: PgPolicyConfig['as'];\n\treadonly for: PgPolicyConfig['for'];\n\treadonly to: PgPolicyConfig['to'];\n\treadonly using: PgPolicyConfig['using'];\n\treadonly withCheck: PgPolicyConfig['withCheck'];\n\n\t/** @internal */\n\t_linkedTable?: PgTable;\n\n\tconstructor(\n\t\treadonly name: string,\n\t\tconfig?: PgPolicyConfig,\n\t) {\n\t\tif (config) {\n\t\t\tthis.as = config.as;\n\t\t\tthis.for = config.for;\n\t\t\tthis.to = config.to;\n\t\t\tthis.using = config.using;\n\t\t\tthis.withCheck = config.withCheck;\n\t\t}\n\t}\n\n\tlink(table: PgTable): this {\n\t\tthis._linkedTable = table;\n\t\treturn this;\n\t}\n}\n\nexport function pgPolicy(name: string, config?: PgPolicyConfig) {\n\treturn new PgPolicy(name, config);\n}\n", "import { entityKind } from '~/entity.ts';\n\nexport interface PgRoleConfig {\n\tcreateDb?: boolean;\n\tcreateRole?: boolean;\n\tinherit?: boolean;\n}\n\nexport class PgRole implements PgRoleConfig {\n\tstatic readonly [entityKind]: string = 'PgRole';\n\n\t/** @internal */\n\t_existing?: boolean;\n\n\t/** @internal */\n\treadonly createDb: PgRoleConfig['createDb'];\n\t/** @internal */\n\treadonly createRole: PgRoleConfig['createRole'];\n\t/** @internal */\n\treadonly inherit: PgRoleConfig['inherit'];\n\n\tconstructor(\n\t\treadonly name: string,\n\t\tconfig?: PgRoleConfig,\n\t) {\n\t\tif (config) {\n\t\t\tthis.createDb = config.createDb;\n\t\t\tthis.createRole = config.createRole;\n\t\t\tthis.inherit = config.inherit;\n\t\t}\n\t}\n\n\texisting(): this {\n\t\tthis._existing = true;\n\t\treturn this;\n\t}\n}\n\nexport function pgRole(name: string, config?: PgRoleConfig) {\n\treturn new PgRole(name, config);\n}\n", "import { entityKind, is } from '~/entity.ts';\n\nexport type PgSequenceOptions = {\n\tincrement?: number | string;\n\tminValue?: number | string;\n\tmaxValue?: number | string;\n\tstartWith?: number | string;\n\tcache?: number | string;\n\tcycle?: boolean;\n};\n\nexport class PgSequence {\n\tstatic readonly [entityKind]: string = 'PgSequence';\n\n\tconstructor(\n\t\tpublic readonly seqName: string | undefined,\n\t\tpublic readonly seqOptions: PgSequenceOptions | undefined,\n\t\tpublic readonly schema: string | undefined,\n\t) {\n\t}\n}\n\nexport function pgSequence(\n\tname: string,\n\toptions?: PgSequenceOptions,\n): PgSequence {\n\treturn pgSequenceWithSchema(name, options, undefined);\n}\n\n/** @internal */\nexport function pgSequenceWithSchema(\n\tname: string,\n\toptions?: PgSequenceOptions,\n\tschema?: string,\n): PgSequence {\n\treturn new PgSequence(name, options, schema);\n}\n\nexport function isPgSequence(obj: unknown): obj is PgSequence {\n\treturn is(obj, PgSequence);\n}\n", "export const PgViewConfig = Symbol.for('drizzle:PgViewConfig');\n", "import type { BuildColumns } from '~/column-builder.ts';\nimport { entityKind, is } from '~/entity.ts';\nimport type { TypedQueryBuilder } from '~/query-builders/query-builder.ts';\nimport type { AddAliasToSelection } from '~/query-builders/select.types.ts';\nimport { SelectionProxyHandler } from '~/selection-proxy.ts';\nimport type { ColumnsSelection, SQL } from '~/sql/sql.ts';\nimport { getTableColumns } from '~/utils.ts';\nimport type { RequireAtLeastOne } from '~/utils.ts';\nimport type { PgColumn, PgColumnBuilderBase } from './columns/common.ts';\nimport { QueryBuilder } from './query-builders/query-builder.ts';\nimport { pgTable } from './table.ts';\nimport { PgViewBase } from './view-base.ts';\nimport { PgViewConfig } from './view-common.ts';\n\nexport type ViewWithConfig = RequireAtLeastOne<{\n\tcheckOption: 'local' | 'cascaded';\n\tsecurityBarrier: boolean;\n\tsecurityInvoker: boolean;\n}>;\n\nexport class DefaultViewBuilderCore<TConfig extends { name: string; columns?: unknown }> {\n\tstatic readonly [entityKind]: string = 'PgDefaultViewBuilderCore';\n\n\tdeclare readonly _: {\n\t\treadonly name: TConfig['name'];\n\t\treadonly columns: TConfig['columns'];\n\t};\n\n\tconstructor(\n\t\tprotected name: TConfig['name'],\n\t\tprotected schema: string | undefined,\n\t) {}\n\n\tprotected config: {\n\t\twith?: ViewWithConfig;\n\t} = {};\n\n\twith(config: ViewWithConfig): this {\n\t\tthis.config.with = config;\n\t\treturn this;\n\t}\n}\n\nexport class ViewBuilder<TName extends string = string> extends DefaultViewBuilderCore<{ name: TName }> {\n\tstatic override readonly [entityKind]: string = 'PgViewBuilder';\n\n\tas<TSelectedFields extends ColumnsSelection>(\n\t\tqb: TypedQueryBuilder<TSelectedFields> | ((qb: QueryBuilder) => TypedQueryBuilder<TSelectedFields>),\n\t): PgViewWithSelection<TName, false, AddAliasToSelection<TSelectedFields, TName, 'pg'>> {\n\t\tif (typeof qb === 'function') {\n\t\t\tqb = qb(new QueryBuilder());\n\t\t}\n\t\tconst selectionProxy = new SelectionProxyHandler<TSelectedFields>({\n\t\t\talias: this.name,\n\t\t\tsqlBehavior: 'error',\n\t\t\tsqlAliasedBehavior: 'alias',\n\t\t\treplaceOriginalName: true,\n\t\t});\n\t\tconst aliasedSelection = new Proxy(qb.getSelectedFields(), selectionProxy);\n\t\treturn new Proxy(\n\t\t\tnew PgView({\n\t\t\t\tpgConfig: this.config,\n\t\t\t\tconfig: {\n\t\t\t\t\tname: this.name,\n\t\t\t\t\tschema: this.schema,\n\t\t\t\t\tselectedFields: aliasedSelection,\n\t\t\t\t\tquery: qb.getSQL().inlineParams(),\n\t\t\t\t},\n\t\t\t}),\n\t\t\tselectionProxy as any,\n\t\t) as PgViewWithSelection<TName, false, AddAliasToSelection<TSelectedFields, TName, 'pg'>>;\n\t}\n}\n\nexport class ManualViewBuilder<\n\tTName extends string = string,\n\tTColumns extends Record<string, PgColumnBuilderBase> = Record<string, PgColumnBuilderBase>,\n> extends DefaultViewBuilderCore<{ name: TName; columns: TColumns }> {\n\tstatic override readonly [entityKind]: string = 'PgManualViewBuilder';\n\n\tprivate columns: Record<string, PgColumn>;\n\n\tconstructor(\n\t\tname: TName,\n\t\tcolumns: TColumns,\n\t\tschema: string | undefined,\n\t) {\n\t\tsuper(name, schema);\n\t\tthis.columns = getTableColumns(pgTable(name, columns));\n\t}\n\n\texisting(): PgViewWithSelection<TName, true, BuildColumns<TName, TColumns, 'pg'>> {\n\t\treturn new Proxy(\n\t\t\tnew PgView({\n\t\t\t\tpgConfig: undefined,\n\t\t\t\tconfig: {\n\t\t\t\t\tname: this.name,\n\t\t\t\t\tschema: this.schema,\n\t\t\t\t\tselectedFields: this.columns,\n\t\t\t\t\tquery: undefined,\n\t\t\t\t},\n\t\t\t}),\n\t\t\tnew SelectionProxyHandler({\n\t\t\t\talias: this.name,\n\t\t\t\tsqlBehavior: 'error',\n\t\t\t\tsqlAliasedBehavior: 'alias',\n\t\t\t\treplaceOriginalName: true,\n\t\t\t}),\n\t\t) as PgViewWithSelection<TName, true, BuildColumns<TName, TColumns, 'pg'>>;\n\t}\n\n\tas(query: SQL): PgViewWithSelection<TName, false, BuildColumns<TName, TColumns, 'pg'>> {\n\t\treturn new Proxy(\n\t\t\tnew PgView({\n\t\t\t\tpgConfig: this.config,\n\t\t\t\tconfig: {\n\t\t\t\t\tname: this.name,\n\t\t\t\t\tschema: this.schema,\n\t\t\t\t\tselectedFields: this.columns,\n\t\t\t\t\tquery: query.inlineParams(),\n\t\t\t\t},\n\t\t\t}),\n\t\t\tnew SelectionProxyHandler({\n\t\t\t\talias: this.name,\n\t\t\t\tsqlBehavior: 'error',\n\t\t\t\tsqlAliasedBehavior: 'alias',\n\t\t\t\treplaceOriginalName: true,\n\t\t\t}),\n\t\t) as PgViewWithSelection<TName, false, BuildColumns<TName, TColumns, 'pg'>>;\n\t}\n}\n\nexport type PgMaterializedViewWithConfig = RequireAtLeastOne<{\n\tfillfactor: number;\n\ttoastTupleTarget: number;\n\tparallelWorkers: number;\n\tautovacuumEnabled: boolean;\n\tvacuumIndexCleanup: 'auto' | 'off' | 'on';\n\tvacuumTruncate: boolean;\n\tautovacuumVacuumThreshold: number;\n\tautovacuumVacuumScaleFactor: number;\n\tautovacuumVacuumCostDelay: number;\n\tautovacuumVacuumCostLimit: number;\n\tautovacuumFreezeMinAge: number;\n\tautovacuumFreezeMaxAge: number;\n\tautovacuumFreezeTableAge: number;\n\tautovacuumMultixactFreezeMinAge: number;\n\tautovacuumMultixactFreezeMaxAge: number;\n\tautovacuumMultixactFreezeTableAge: number;\n\tlogAutovacuumMinDuration: number;\n\tuserCatalogTable: boolean;\n}>;\n\nexport class MaterializedViewBuilderCore<TConfig extends { name: string; columns?: unknown }> {\n\tstatic readonly [entityKind]: string = 'PgMaterializedViewBuilderCore';\n\n\tdeclare _: {\n\t\treadonly name: TConfig['name'];\n\t\treadonly columns: TConfig['columns'];\n\t};\n\n\tconstructor(\n\t\tprotected name: TConfig['name'],\n\t\tprotected schema: string | undefined,\n\t) {}\n\n\tprotected config: {\n\t\twith?: PgMaterializedViewWithConfig;\n\t\tusing?: string;\n\t\ttablespace?: string;\n\t\twithNoData?: boolean;\n\t} = {};\n\n\tusing(using: string): this {\n\t\tthis.config.using = using;\n\t\treturn this;\n\t}\n\n\twith(config: PgMaterializedViewWithConfig): this {\n\t\tthis.config.with = config;\n\t\treturn this;\n\t}\n\n\ttablespace(tablespace: string): this {\n\t\tthis.config.tablespace = tablespace;\n\t\treturn this;\n\t}\n\n\twithNoData(): this {\n\t\tthis.config.withNoData = true;\n\t\treturn this;\n\t}\n}\n\nexport class MaterializedViewBuilder<TName extends string = string>\n\textends MaterializedViewBuilderCore<{ name: TName }>\n{\n\tstatic override readonly [entityKind]: string = 'PgMaterializedViewBuilder';\n\n\tas<TSelectedFields extends ColumnsSelection>(\n\t\tqb: TypedQueryBuilder<TSelectedFields> | ((qb: QueryBuilder) => TypedQueryBuilder<TSelectedFields>),\n\t): PgMaterializedViewWithSelection<TName, false, AddAliasToSelection<TSelectedFields, TName, 'pg'>> {\n\t\tif (typeof qb === 'function') {\n\t\t\tqb = qb(new QueryBuilder());\n\t\t}\n\t\tconst selectionProxy = new SelectionProxyHandler<TSelectedFields>({\n\t\t\talias: this.name,\n\t\t\tsqlBehavior: 'error',\n\t\t\tsqlAliasedBehavior: 'alias',\n\t\t\treplaceOriginalName: true,\n\t\t});\n\t\tconst aliasedSelection = new Proxy(qb.getSelectedFields(), selectionProxy);\n\t\treturn new Proxy(\n\t\t\tnew PgMaterializedView({\n\t\t\t\tpgConfig: {\n\t\t\t\t\twith: this.config.with,\n\t\t\t\t\tusing: this.config.using,\n\t\t\t\t\ttablespace: this.config.tablespace,\n\t\t\t\t\twithNoData: this.config.withNoData,\n\t\t\t\t},\n\t\t\t\tconfig: {\n\t\t\t\t\tname: this.name,\n\t\t\t\t\tschema: this.schema,\n\t\t\t\t\tselectedFields: aliasedSelection,\n\t\t\t\t\tquery: qb.getSQL().inlineParams(),\n\t\t\t\t},\n\t\t\t}),\n\t\t\tselectionProxy as any,\n\t\t) as PgMaterializedViewWithSelection<TName, false, AddAliasToSelection<TSelectedFields, TName, 'pg'>>;\n\t}\n}\n\nexport class ManualMaterializedViewBuilder<\n\tTName extends string = string,\n\tTColumns extends Record<string, PgColumnBuilderBase> = Record<string, PgColumnBuilderBase>,\n> extends MaterializedViewBuilderCore<{ name: TName; columns: TColumns }> {\n\tstatic override readonly [entityKind]: string = 'PgManualMaterializedViewBuilder';\n\n\tprivate columns: Record<string, PgColumn>;\n\n\tconstructor(\n\t\tname: TName,\n\t\tcolumns: TColumns,\n\t\tschema: string | undefined,\n\t) {\n\t\tsuper(name, schema);\n\t\tthis.columns = getTableColumns(pgTable(name, columns));\n\t}\n\n\texisting(): PgMaterializedViewWithSelection<TName, true, BuildColumns<TName, TColumns, 'pg'>> {\n\t\treturn new Proxy(\n\t\t\tnew PgMaterializedView({\n\t\t\t\tpgConfig: {\n\t\t\t\t\ttablespace: this.config.tablespace,\n\t\t\t\t\tusing: this.config.using,\n\t\t\t\t\twith: this.config.with,\n\t\t\t\t\twithNoData: this.config.withNoData,\n\t\t\t\t},\n\t\t\t\tconfig: {\n\t\t\t\t\tname: this.name,\n\t\t\t\t\tschema: this.schema,\n\t\t\t\t\tselectedFields: this.columns,\n\t\t\t\t\tquery: undefined,\n\t\t\t\t},\n\t\t\t}),\n\t\t\tnew SelectionProxyHandler({\n\t\t\t\talias: this.name,\n\t\t\t\tsqlBehavior: 'error',\n\t\t\t\tsqlAliasedBehavior: 'alias',\n\t\t\t\treplaceOriginalName: true,\n\t\t\t}),\n\t\t) as PgMaterializedViewWithSelection<TName, true, BuildColumns<TName, TColumns, 'pg'>>;\n\t}\n\n\tas(query: SQL): PgMaterializedViewWithSelection<TName, false, BuildColumns<TName, TColumns, 'pg'>> {\n\t\treturn new Proxy(\n\t\t\tnew PgMaterializedView({\n\t\t\t\tpgConfig: {\n\t\t\t\t\ttablespace: this.config.tablespace,\n\t\t\t\t\tusing: this.config.using,\n\t\t\t\t\twith: this.config.with,\n\t\t\t\t\twithNoData: this.config.withNoData,\n\t\t\t\t},\n\t\t\t\tconfig: {\n\t\t\t\t\tname: this.name,\n\t\t\t\t\tschema: this.schema,\n\t\t\t\t\tselectedFields: this.columns,\n\t\t\t\t\tquery: query.inlineParams(),\n\t\t\t\t},\n\t\t\t}),\n\t\t\tnew SelectionProxyHandler({\n\t\t\t\talias: this.name,\n\t\t\t\tsqlBehavior: 'error',\n\t\t\t\tsqlAliasedBehavior: 'alias',\n\t\t\t\treplaceOriginalName: true,\n\t\t\t}),\n\t\t) as PgMaterializedViewWithSelection<TName, false, BuildColumns<TName, TColumns, 'pg'>>;\n\t}\n}\n\nexport class PgView<\n\tTName extends string = string,\n\tTExisting extends boolean = boolean,\n\tTSelectedFields extends ColumnsSelection = ColumnsSelection,\n> extends PgViewBase<TName, TExisting, TSelectedFields> {\n\tstatic override readonly [entityKind]: string = 'PgView';\n\n\t[PgViewConfig]: {\n\t\twith?: ViewWithConfig;\n\t} | undefined;\n\n\tconstructor({ pgConfig, config }: {\n\t\tpgConfig: {\n\t\t\twith?: ViewWithConfig;\n\t\t} | undefined;\n\t\tconfig: {\n\t\t\tname: TName;\n\t\t\tschema: string | undefined;\n\t\t\tselectedFields: ColumnsSelection;\n\t\t\tquery: SQL | undefined;\n\t\t};\n\t}) {\n\t\tsuper(config);\n\t\tif (pgConfig) {\n\t\t\tthis[PgViewConfig] = {\n\t\t\t\twith: pgConfig.with,\n\t\t\t};\n\t\t}\n\t}\n}\n\nexport type PgViewWithSelection<\n\tTName extends string = string,\n\tTExisting extends boolean = boolean,\n\tTSelectedFields extends ColumnsSelection = ColumnsSelection,\n> = PgView<TName, TExisting, TSelectedFields> & TSelectedFields;\n\nexport const PgMaterializedViewConfig = Symbol.for('drizzle:PgMaterializedViewConfig');\n\nexport class PgMaterializedView<\n\tTName extends string = string,\n\tTExisting extends boolean = boolean,\n\tTSelectedFields extends ColumnsSelection = ColumnsSelection,\n> extends PgViewBase<TName, TExisting, TSelectedFields> {\n\tstatic override readonly [entityKind]: string = 'PgMaterializedView';\n\n\treadonly [PgMaterializedViewConfig]: {\n\t\treadonly with?: PgMaterializedViewWithConfig;\n\t\treadonly using?: string;\n\t\treadonly tablespace?: string;\n\t\treadonly withNoData?: boolean;\n\t} | undefined;\n\n\tconstructor({ pgConfig, config }: {\n\t\tpgConfig: {\n\t\t\twith: PgMaterializedViewWithConfig | undefined;\n\t\t\tusing: string | undefined;\n\t\t\ttablespace: string | undefined;\n\t\t\twithNoData: boolean | undefined;\n\t\t} | undefined;\n\t\tconfig: {\n\t\t\tname: TName;\n\t\t\tschema: string | undefined;\n\t\t\tselectedFields: ColumnsSelection;\n\t\t\tquery: SQL | undefined;\n\t\t};\n\t}) {\n\t\tsuper(config);\n\t\tthis[PgMaterializedViewConfig] = {\n\t\t\twith: pgConfig?.with,\n\t\t\tusing: pgConfig?.using,\n\t\t\ttablespace: pgConfig?.tablespace,\n\t\t\twithNoData: pgConfig?.withNoData,\n\t\t};\n\t}\n}\n\nexport type PgMaterializedViewWithSelection<\n\tTName extends string = string,\n\tTExisting extends boolean = boolean,\n\tTSelectedFields extends ColumnsSelection = ColumnsSelection,\n> = PgMaterializedView<TName, TExisting, TSelectedFields> & TSelectedFields;\n\n/** @internal */\nexport function pgViewWithSchema(\n\tname: string,\n\tselection: Record<string, PgColumnBuilderBase> | undefined,\n\tschema: string | undefined,\n): ViewBuilder | ManualViewBuilder {\n\tif (selection) {\n\t\treturn new ManualViewBuilder(name, selection, schema);\n\t}\n\treturn new ViewBuilder(name, schema);\n}\n\n/** @internal */\nexport function pgMaterializedViewWithSchema(\n\tname: string,\n\tselection: Record<string, PgColumnBuilderBase> | undefined,\n\tschema: string | undefined,\n): MaterializedViewBuilder | ManualMaterializedViewBuilder {\n\tif (selection) {\n\t\treturn new ManualMaterializedViewBuilder(name, selection, schema);\n\t}\n\treturn new MaterializedViewBuilder(name, schema);\n}\n\nexport function pgView<TName extends string>(name: TName): ViewBuilder<TName>;\nexport function pgView<TName extends string, TColumns extends Record<string, PgColumnBuilderBase>>(\n\tname: TName,\n\tcolumns: TColumns,\n): ManualViewBuilder<TName, TColumns>;\nexport function pgView(name: string, columns?: Record<string, PgColumnBuilderBase>): ViewBuilder | ManualViewBuilder {\n\treturn pgViewWithSchema(name, columns, undefined);\n}\n\nexport function pgMaterializedView<TName extends string>(name: TName): MaterializedViewBuilder<TName>;\nexport function pgMaterializedView<TName extends string, TColumns extends Record<string, PgColumnBuilderBase>>(\n\tname: TName,\n\tcolumns: TColumns,\n): ManualMaterializedViewBuilder<TName, TColumns>;\nexport function pgMaterializedView(\n\tname: string,\n\tcolumns?: Record<string, PgColumnBuilderBase>,\n): MaterializedViewBuilder | ManualMaterializedViewBuilder {\n\treturn pgMaterializedViewWithSchema(name, columns, undefined);\n}\n\nexport function isPgView(obj: unknown): obj is PgView {\n\treturn is(obj, PgView);\n}\n\nexport function isPgMaterializedView(obj: unknown): obj is PgMaterializedView {\n\treturn is(obj, PgMaterializedView);\n}\n", "import { entityKind, is } from '~/entity.ts';\nimport { SQL, sql, type SQLWrapper } from '~/sql/sql.ts';\nimport type { pgEnum } from './columns/enum.ts';\nimport { pgEnumWithSchema } from './columns/enum.ts';\nimport { type pgSequence, pgSequenceWithSchema } from './sequence.ts';\nimport { type PgTableFn, pgTableWithSchema } from './table.ts';\nimport { type pgMaterializedView, pgMaterializedViewWithSchema, type pgView, pgViewWithSchema } from './view.ts';\n\nexport class PgSchema<TName extends string = string> implements SQLWrapper {\n\tstatic readonly [entityKind]: string = 'PgSchema';\n\tconstructor(\n\t\tpublic readonly schemaName: TName,\n\t) {}\n\n\ttable: PgTableFn<TName> = ((name, columns, extraConfig) => {\n\t\treturn pgTableWithSchema(name, columns, extraConfig, this.schemaName);\n\t});\n\n\tview = ((name, columns) => {\n\t\treturn pgViewWithSchema(name, columns, this.schemaName);\n\t}) as typeof pgView;\n\n\tmaterializedView = ((name, columns) => {\n\t\treturn pgMaterializedViewWithSchema(name, columns, this.schemaName);\n\t}) as typeof pgMaterializedView;\n\n\tenum: typeof pgEnum = ((name, values) => {\n\t\treturn pgEnumWithSchema(name, values, this.schemaName);\n\t});\n\n\tsequence: typeof pgSequence = ((name, options) => {\n\t\treturn pgSequenceWithSchema(name, options, this.schemaName);\n\t});\n\n\tgetSQL(): SQL {\n\t\treturn new SQL([sql.identifier(this.schemaName)]);\n\t}\n\n\tshouldOmitSQLParens(): boolean {\n\t\treturn true;\n\t}\n}\n\nexport function isPgSchema(obj: unknown): obj is PgSchema {\n\treturn is(obj, PgSchema);\n}\n\nexport function pgSchema<T extends string>(name: T) {\n\tif (name === 'public') {\n\t\tthrow new Error(\n\t\t\t`You can't specify 'public' as schema name. Postgres is using public schema by default. If you want to use 'public' schema, just use pgTable() instead of creating a schema`,\n\t\t);\n\t}\n\n\treturn new PgSchema(name);\n}\n", "import { entityKind } from '~/entity.ts';\nimport { TransactionRollbackError } from '~/errors.ts';\nimport type { TablesRelationalConfig } from '~/relations.ts';\nimport type { PreparedQuery } from '~/session.ts';\nimport { type Query, type SQL, sql } from '~/sql/index.ts';\nimport { tracer } from '~/tracing.ts';\nimport type { NeonAuthToken } from '~/utils.ts';\nimport { PgDatabase } from './db.ts';\nimport type { PgDialect } from './dialect.ts';\nimport type { SelectedFieldsOrdered } from './query-builders/select.types.ts';\n\nexport interface PreparedQueryConfig {\n\texecute: unknown;\n\tall: unknown;\n\tvalues: unknown;\n}\n\nexport abstract class PgPreparedQuery<T extends PreparedQueryConfig> implements PreparedQuery {\n\tconstructor(protected query: Query) {}\n\n\tprotected authToken?: NeonAuthToken;\n\n\tgetQuery(): Query {\n\t\treturn this.query;\n\t}\n\n\tmapResult(response: unknown, _isFromBatch?: boolean): unknown {\n\t\treturn response;\n\t}\n\n\t/** @internal */\n\tsetToken(token?: NeonAuthToken) {\n\t\tthis.authToken = token;\n\t\treturn this;\n\t}\n\n\tstatic readonly [entityKind]: string = 'PgPreparedQuery';\n\n\t/** @internal */\n\tjoinsNotNullableMap?: Record<string, boolean>;\n\n\tabstract execute(placeholderValues?: Record<string, unknown>): Promise<T['execute']>;\n\t/** @internal */\n\tabstract execute(placeholderValues?: Record<string, unknown>, token?: NeonAuthToken): Promise<T['execute']>;\n\t/** @internal */\n\tabstract execute(placeholderValues?: Record<string, unknown>, token?: NeonAuthToken): Promise<T['execute']>;\n\n\t/** @internal */\n\tabstract all(placeholderValues?: Record<string, unknown>): Promise<T['all']>;\n\n\t/** @internal */\n\tabstract isResponseInArrayMode(): boolean;\n}\n\nexport interface PgTransactionConfig {\n\tisolationLevel?: 'read uncommitted' | 'read committed' | 'repeatable read' | 'serializable';\n\taccessMode?: 'read only' | 'read write';\n\tdeferrable?: boolean;\n}\n\nexport abstract class PgSession<\n\tTQueryResult extends PgQueryResultHKT = PgQueryResultHKT,\n\tTFullSchema extends Record<string, unknown> = Record<string, never>,\n\tTSchema extends TablesRelationalConfig = Record<string, never>,\n> {\n\tstatic readonly [entityKind]: string = 'PgSession';\n\n\tconstructor(protected dialect: PgDialect) {}\n\n\tabstract prepareQuery<T extends PreparedQueryConfig = PreparedQueryConfig>(\n\t\tquery: Query,\n\t\tfields: SelectedFieldsOrdered | undefined,\n\t\tname: string | undefined,\n\t\tisResponseInArrayMode: boolean,\n\t\tcustomResultMapper?: (rows: unknown[][], mapColumnValue?: (value: unknown) => unknown) => T['execute'],\n\t): PgPreparedQuery<T>;\n\n\texecute<T>(query: SQL): Promise<T>;\n\t/** @internal */\n\texecute<T>(query: SQL, token?: NeonAuthToken): Promise<T>;\n\t/** @internal */\n\texecute<T>(query: SQL, token?: NeonAuthToken): Promise<T> {\n\t\treturn tracer.startActiveSpan('drizzle.operation', () => {\n\t\t\tconst prepared = tracer.startActiveSpan('drizzle.prepareQuery', () => {\n\t\t\t\treturn this.prepareQuery<PreparedQueryConfig & { execute: T }>(\n\t\t\t\t\tthis.dialect.sqlToQuery(query),\n\t\t\t\t\tundefined,\n\t\t\t\t\tundefined,\n\t\t\t\t\tfalse,\n\t\t\t\t);\n\t\t\t});\n\n\t\t\treturn prepared.setToken(token).execute(undefined, token);\n\t\t});\n\t}\n\n\tall<T = unknown>(query: SQL): Promise<T[]> {\n\t\treturn this.prepareQuery<PreparedQueryConfig & { all: T[] }>(\n\t\t\tthis.dialect.sqlToQuery(query),\n\t\t\tundefined,\n\t\t\tundefined,\n\t\t\tfalse,\n\t\t).all();\n\t}\n\n\tasync count(sql: SQL): Promise<number>;\n\t/** @internal */\n\tasync count(sql: SQL, token?: NeonAuthToken): Promise<number>;\n\t/** @internal */\n\tasync count(sql: SQL, token?: NeonAuthToken): Promise<number> {\n\t\tconst res = await this.execute<[{ count: string }]>(sql, token);\n\n\t\treturn Number(\n\t\t\tres[0]['count'],\n\t\t);\n\t}\n\n\tabstract transaction<T>(\n\t\ttransaction: (tx: PgTransaction<TQueryResult, TFullSchema, TSchema>) => Promise<T>,\n\t\tconfig?: PgTransactionConfig,\n\t): Promise<T>;\n}\n\nexport abstract class PgTransaction<\n\tTQueryResult extends PgQueryResultHKT,\n\tTFullSchema extends Record<string, unknown> = Record<string, never>,\n\tTSchema extends TablesRelationalConfig = Record<string, never>,\n> extends PgDatabase<TQueryResult, TFullSchema, TSchema> {\n\tstatic override readonly [entityKind]: string = 'PgTransaction';\n\n\tconstructor(\n\t\tdialect: PgDialect,\n\t\tsession: PgSession<any, any, any>,\n\t\tprotected schema: {\n\t\t\tfullSchema: Record<string, unknown>;\n\t\t\tschema: TSchema;\n\t\t\ttableNamesMap: Record<string, string>;\n\t\t} | undefined,\n\t\tprotected readonly nestedIndex = 0,\n\t) {\n\t\tsuper(dialect, session, schema);\n\t}\n\n\trollback(): never {\n\t\tthrow new TransactionRollbackError();\n\t}\n\n\t/** @internal */\n\tgetTransactionConfigSQL(config: PgTransactionConfig): SQL {\n\t\tconst chunks: string[] = [];\n\t\tif (config.isolationLevel) {\n\t\t\tchunks.push(`isolation level ${config.isolationLevel}`);\n\t\t}\n\t\tif (config.accessMode) {\n\t\t\tchunks.push(config.accessMode);\n\t\t}\n\t\tif (typeof config.deferrable === 'boolean') {\n\t\t\tchunks.push(config.deferrable ? 'deferrable' : 'not deferrable');\n\t\t}\n\t\treturn sql.raw(chunks.join(' '));\n\t}\n\n\tsetTransaction(config: PgTransactionConfig): Promise<void> {\n\t\treturn this.session.execute(sql`set transaction ${this.getTransactionConfigSQL(config)}`);\n\t}\n\n\tabstract override transaction<T>(\n\t\ttransaction: (tx: PgTransaction<TQueryResult, TFullSchema, TSchema>) => Promise<T>,\n\t): Promise<T>;\n}\n\nexport interface PgQueryResultHKT {\n\treadonly $brand: 'PgQueryResultHKT';\n\treadonly row: unknown;\n\treadonly type: unknown;\n}\n\nexport type PgQueryResultKind<TKind extends PgQueryResultHKT, TRow> = (TKind & {\n\treadonly row: TRow;\n})['type'];\n", "import { is } from '~/entity.ts';\nimport { PgTable } from '~/pg-core/table.ts';\nimport { Table } from '~/table.ts';\nimport { ViewBaseConfig } from '~/view-common.ts';\nimport { type Check, CheckBuilder } from './checks.ts';\nimport type { AnyPgColumn } from './columns/index.ts';\nimport { type ForeignKey, ForeignKeyBuilder } from './foreign-keys.ts';\nimport type { Index } from './indexes.ts';\nimport { IndexBuilder } from './indexes.ts';\nimport { PgPolicy } from './policies.ts';\nimport { type PrimaryKey, PrimaryKeyBuilder } from './primary-keys.ts';\nimport { type UniqueConstraint, UniqueConstraintBuilder } from './unique-constraint.ts';\nimport { PgViewConfig } from './view-common.ts';\nimport { type PgMaterializedView, PgMaterializedViewConfig, type PgView } from './view.ts';\n\nexport function getTableConfig<TTable extends PgTable>(table: TTable) {\n\tconst columns = Object.values(table[Table.Symbol.Columns]);\n\tconst indexes: Index[] = [];\n\tconst checks: Check[] = [];\n\tconst primaryKeys: PrimaryKey[] = [];\n\tconst foreignKeys: ForeignKey[] = Object.values(table[PgTable.Symbol.InlineForeignKeys]);\n\tconst uniqueConstraints: UniqueConstraint[] = [];\n\tconst name = table[Table.Symbol.Name];\n\tconst schema = table[Table.Symbol.Schema];\n\tconst policies: PgPolicy[] = [];\n\tconst enableRLS: boolean = table[PgTable.Symbol.EnableRLS];\n\n\tconst extraConfigBuilder = table[PgTable.Symbol.ExtraConfigBuilder];\n\n\tif (extraConfigBuilder !== undefined) {\n\t\tconst extraConfig = extraConfigBuilder(table[Table.Symbol.ExtraConfigColumns]);\n\t\tconst extraValues = Array.isArray(extraConfig) ? extraConfig.flat(1) as any[] : Object.values(extraConfig);\n\t\tfor (const builder of extraValues) {\n\t\t\tif (is(builder, IndexBuilder)) {\n\t\t\t\tindexes.push(builder.build(table));\n\t\t\t} else if (is(builder, CheckBuilder)) {\n\t\t\t\tchecks.push(builder.build(table));\n\t\t\t} else if (is(builder, UniqueConstraintBuilder)) {\n\t\t\t\tuniqueConstraints.push(builder.build(table));\n\t\t\t} else if (is(builder, PrimaryKeyBuilder)) {\n\t\t\t\tprimaryKeys.push(builder.build(table));\n\t\t\t} else if (is(builder, ForeignKeyBuilder)) {\n\t\t\t\tforeignKeys.push(builder.build(table));\n\t\t\t} else if (is(builder, PgPolicy)) {\n\t\t\t\tpolicies.push(builder);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn {\n\t\tcolumns,\n\t\tindexes,\n\t\tforeignKeys,\n\t\tchecks,\n\t\tprimaryKeys,\n\t\tuniqueConstraints,\n\t\tname,\n\t\tschema,\n\t\tpolicies,\n\t\tenableRLS,\n\t};\n}\n\nexport function getViewConfig<\n\tTName extends string = string,\n\tTExisting extends boolean = boolean,\n>(view: PgView<TName, TExisting>) {\n\treturn {\n\t\t...view[ViewBaseConfig],\n\t\t...view[PgViewConfig],\n\t};\n}\n\nexport function getMaterializedViewConfig<\n\tTName extends string = string,\n\tTExisting extends boolean = boolean,\n>(view: PgMaterializedView<TName, TExisting>) {\n\treturn {\n\t\t...view[ViewBaseConfig],\n\t\t...view[PgMaterializedViewConfig],\n\t};\n}\n\nexport type ColumnsWithTable<\n\tTTableName extends string,\n\tTForeignTableName extends string,\n\tTColumns extends AnyPgColumn<{ tableName: TTableName }>[],\n> = { [Key in keyof TColumns]: AnyPgColumn<{ tableName: TForeignTableName }> };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMO,SAAS,MACf,OACAA,QACkC;AAClC,SAAO,IAAI,MAAM,OAAO,IAAI,uBAAuBA,QAAO,KAAK,CAAC;AACjE;;;ACXA;AAKkB;AADX,IAAM,eAAN,MAAmB;EAKzB,YAAmB,MAAqB,OAAY;AAF1C;AAES,SAAA,OAAA;AAAqB,SAAA,QAAA;EAAa;;EAGrD,MAAM,OAAuB;AAC5B,WAAO,IAAI,MAAM,OAAO,IAAI;EAC7B;AACD;AAVC,cADY,cACK,IAAsB;AALxC,IAAAC;AAkBkBA,MAAA;AADX,IAAM,QAAN,MAAY;EAMlB,YAAmB,OAAgB,SAAuB;AAHjD;AACA;AAEU,SAAA,QAAA;AAClB,SAAK,OAAO,QAAQ;AACpB,SAAK,QAAQ,QAAQ;EACtB;AACD;AATC,cADY,OACKA,KAAsB;AAWjC,SAAS,MAAM,MAAc,OAA0B;AAC7D,SAAO,IAAI,aAAa,MAAM,KAAK;AACpC;;;AC/BA,IAAAC;AAUkBA,MAAA;AAHX,IAAM,yBAAN,MAAM,uBAEb;EAiCC,YAAY,QAA4C;AA9BhD;AA+BP,SAAK,SAAS,EAAE,GAAG,OAAO;EAC3B;EAEA,IAAI,UAAa,MAA4B;AAC5C,QAAI,SAAS,KAAK;AACjB,aAAO;QACN,GAAG,SAAS,GAA4B;QACxC,gBAAgB,IAAI;UAClB,SAAsB,EAAE;UACzB;QACD;MACD;IACD;AAEA,QAAI,SAAS,gBAAgB;AAC5B,aAAO;QACN,GAAG,SAAS,cAAuC;QACnD,gBAAgB,IAAI;UAClB,SAAkB,cAAc,EAAE;UACnC;QACD;MACD;IACD;AAEA,QAAI,OAAO,SAAS,UAAU;AAC7B,aAAO,SAAS,IAA6B;IAC9C;AAEA,UAAM,UAAU,GAAG,UAAU,QAAQ,IAClC,SAAS,EAAE,iBACX,GAAG,UAAU,IAAI,IACjB,SAAS,cAAc,EAAE,iBACzB;AACH,UAAM,QAAiB,QAAQ,IAA4B;AAE3D,QAAI,GAAG,OAAO,IAAI,OAAO,GAAG;AAE3B,UAAI,KAAK,OAAO,uBAAuB,SAAS,CAAC,MAAM,kBAAkB;AACxE,eAAO,MAAM;MACd;AAEA,YAAM,WAAW,MAAM,MAAM;AAC7B,eAAS,mBAAmB;AAC5B,aAAO;IACR;AAEA,QAAI,GAAG,OAAO,GAAG,GAAG;AACnB,UAAI,KAAK,OAAO,gBAAgB,OAAO;AACtC,eAAO;MACR;AAEA,YAAM,IAAI;QACT,2BAA2B,IAAI;MAChC;IACD;AAEA,QAAI,GAAG,OAAO,MAAM,GAAG;AACtB,UAAI,KAAK,OAAO,OAAO;AACtB,eAAO,IAAI;UACV;UACA,IAAI;YACH,IAAI;cACH,MAAM;cACN,IAAI,uBAAuB,KAAK,OAAO,OAAO,KAAK,OAAO,uBAAuB,KAAK;YACvF;UACD;QACD;MACD;AACA,aAAO;IACR;AAEA,QAAI,OAAO,UAAU,YAAY,UAAU,MAAM;AAChD,aAAO;IACR;AAEA,WAAO,IAAI,MAAM,OAAO,IAAI,uBAAsB,KAAK,MAAM,CAAC;EAC/D;AACD;AA9GC,cAHY,wBAGKA,KAAsB;AAHjC,IAAM,wBAAN;;;ACPP,IAAAC,KAAA;AAoIO,IAAM,eAAN,eAQG,mBASiBA,MAAA,YATjB,IAQV;EAKC,YACC,OACQ,SACA,SACR,UACC;AACD,UAAM;AARC;AAuGA;AAOC,mCAAkD,CAAC,sBAAsB;AACjF,aAAO,OAAO,gBAAgB,qBAAqB,MAAM;AACxD,eAAO,KAAK,SAAS,EAAE,QAAQ,mBAAmB,KAAK,SAAS;MACjE,CAAC;IACF;AA9GS,SAAA,UAAA;AACA,SAAA,UAAA;AAIR,SAAK,SAAS,EAAE,OAAO,SAAS;EACjC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA+BA,MAAM,OAAkE;AACvE,SAAK,OAAO,QAAQ;AACpB,WAAO;EACR;EA0BA,UACC,SAA6B,KAAK,OAAO,MAAM,MAAM,OAAO,OAAO,GAC1B;AACzC,SAAK,OAAO,kBAAkB;AAC9B,SAAK,OAAO,YAAY,oBAA8B,MAAM;AAC5D,WAAO;EACR;;EAGA,SAAc;AACb,WAAO,KAAK,QAAQ,iBAAiB,KAAK,MAAM;EACjD;EAEA,QAAe;AACd,UAAM,EAAE,SAAS,UAAU,GAAG,KAAK,IAAI,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC;AAC5E,WAAO;EACR;;EAGA,SAAS,MAAsC;AAC9C,WAAO,OAAO,gBAAgB,wBAAwB,MAAM;AAC3D,aAAO,KAAK,QAAQ,aAIlB,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC,GAAG,KAAK,OAAO,WAAW,MAAM,IAAI;IAC5E,CAAC;EACF;EAEA,QAAQ,MAAqC;AAC5C,WAAO,KAAK,SAAS,IAAI;EAC1B;;EAIA,SAAS,OAAuB;AAC/B,SAAK,YAAY;AACjB,WAAO;EACR;;EASA,oBAAiD;AAChD,WACC,KAAK,OAAO,kBACT,IAAI;MACL,KAAK,OAAO;MACZ,IAAI,sBAAsB;QACzB,OAAO,aAAa,KAAK,OAAO,KAAK;QACrC,oBAAoB;QACpB,aAAa;MACd,CAAC;IACF,IACE;EAEL;EAEA,WAAkC;AACjC,WAAO;EACR;AACD;AAzIC,cAjBY,cAiBcA,KAAsB;;;AChJ1C,SAAS,YAAY,OAAe;AAC1C,QAAM,QAAQ,MACZ,QAAQ,cAAc,EAAE,EACxB,MAAM,yCAAyC,KAAK,CAAC;AAEvD,SAAO,MAAM,IAAI,CAAC,SAAS,KAAK,YAAY,CAAC,EAAE,KAAK,GAAG;AACxD;AAEO,SAAS,YAAY,OAAe;AAC1C,QAAM,QAAQ,MACZ,QAAQ,cAAc,EAAE,EACxB,MAAM,yCAAyC,KAAK,CAAC;AAEvD,SAAO,MAAM,OAAO,CAAC,KAAK,MAAM,MAAM;AACrC,UAAM,gBAAgB,MAAM,IAAI,KAAK,YAAY,IAAI,GAAG,KAAK,CAAC,EAAG,YAAY,CAAC,GAAG,KAAK,MAAM,CAAC,CAAC;AAC9F,WAAO,MAAM;EACd,GAAG,EAAE;AACN;AAEA,SAAS,SAAS,OAAe;AAChC,SAAO;AACR;AAzBA,IAAAC;AA4BkBA,MAAA;AADX,IAAM,cAAN,MAAkB;EAQxB,YAAY,QAAiB;AAJ7B;iCAAgC,CAAC;AACzB,wCAAqC,CAAC;AACtC;AAGP,SAAK,UAAU,WAAW,eACvB,cACA,WAAW,cACX,cACA;EACJ;EAEA,gBAAgB,QAAwB;AACvC,QAAI,CAAC,OAAO;AAAW,aAAO,OAAO;AAErC,UAAM,SAAS,OAAO,MAAM,MAAM,OAAO,MAAM,KAAK;AACpD,UAAM,YAAY,OAAO,MAAM,MAAM,OAAO,YAAY;AACxD,UAAM,MAAM,GAAG,MAAM,IAAI,SAAS,IAAI,OAAO,IAAI;AAEjD,QAAI,CAAC,KAAK,MAAM,GAAG,GAAG;AACrB,WAAK,WAAW,OAAO,KAAK;IAC7B;AACA,WAAO,KAAK,MAAM,GAAG;EACtB;EAEQ,WAAW,OAAc;AAChC,UAAM,SAAS,MAAM,MAAM,OAAO,MAAM,KAAK;AAC7C,UAAM,YAAY,MAAM,MAAM,OAAO,YAAY;AACjD,UAAM,WAAW,GAAG,MAAM,IAAI,SAAS;AAEvC,QAAI,CAAC,KAAK,aAAa,QAAQ,GAAG;AACjC,iBAAW,UAAU,OAAO,OAAO,MAAM,MAAM,OAAO,OAAO,CAAC,GAAG;AAChE,cAAM,YAAY,GAAG,QAAQ,IAAI,OAAO,IAAI;AAC5C,aAAK,MAAM,SAAS,IAAI,KAAK,QAAQ,OAAO,IAAI;MACjD;AACA,WAAK,aAAa,QAAQ,IAAI;IAC/B;EACD;EAEA,aAAa;AACZ,SAAK,QAAQ,CAAC;AACd,SAAK,eAAe,CAAC;EACtB;AACD;AA9CC,cADY,aACKA,KAAsB;;;AC7BxC,IAAAC,KAAAC;AAGO,IAAe,aAAf,eAIGA,MAAA,MACiBD,MAAA,YADjBC,KAAwC;AAMlD;AALC,cALqB,YAKKD,KAAsB;;;ACRjD,IAAAE;AA+DkBA,MAAA;AADX,IAAM,YAAN,MAAgB;EAMtB,YAAY,QAA0B;AAF7B;;AAGR,SAAK,SAAS,IAAI,YAAY,iCAAQ,MAAM;EAC7C;EAEA,MAAM,QAAQ,YAA6B,SAAoB,QAAiD;AAC/G,UAAM,kBAAkB,OAAO,WAAW,WACvC,yBACA,OAAO,mBAAmB;AAC7B,UAAM,mBAAmB,OAAO,WAAW,WAAW,YAAY,OAAO,oBAAoB;AAC7F,UAAM,uBAAuB;gCACC,IAAI,WAAW,gBAAgB,CAAC,IAAI,IAAI,WAAW,eAAe,CAAC;;;;;;AAMjG,UAAM,QAAQ,QAAQ,kCAAkC,IAAI,WAAW,gBAAgB,CAAC,EAAE;AAC1F,UAAM,QAAQ,QAAQ,oBAAoB;AAE1C,UAAM,eAAe,MAAM,QAAQ;MAClC,uCAAuC,IAAI,WAAW,gBAAgB,CAAC,IACtE,IAAI,WAAW,eAAe,CAC/B;IACD;AAEA,UAAM,kBAAkB,aAAa,CAAC;AACtC,UAAM,QAAQ,YAAY,OAAO,OAAO;AACvC,uBAAiB,aAAa,YAAY;AACzC,YACC,CAAC,mBACE,OAAO,gBAAgB,UAAU,IAAI,UAAU,cACjD;AACD,qBAAW,QAAQ,UAAU,KAAK;AACjC,kBAAM,GAAG,QAAQ,IAAI,IAAI,IAAI,CAAC;UAC/B;AACA,gBAAM,GAAG;YACR,kBAAkB,IAAI,WAAW,gBAAgB,CAAC,IACjD,IAAI,WAAW,eAAe,CAC/B,kCAAkC,UAAU,IAAI,KAAK,UAAU,YAAY;UAC5E;QACD;MACD;IACD,CAAC;EACF;EAEA,WAAW,MAAsB;AAChC,WAAO,IAAI,IAAI;EAChB;EAEA,YAAY,KAAqB;AAChC,WAAO,IAAI,MAAM,CAAC;EACnB;EAEA,aAAa,KAAqB;AACjC,WAAO,IAAI,IAAI,QAAQ,MAAM,IAAI,CAAC;EACnC;EAEQ,aAAa,SAAkD;AACtE,QAAI,EAAC,mCAAS;AAAQ,aAAO;AAE7B,UAAM,gBAAgB,CAAC,UAAU;AACjC,eAAW,CAAC,GAAG,CAAC,KAAK,QAAQ,QAAQ,GAAG;AACvC,oBAAc,KAAK,MAAM,IAAI,WAAW,EAAE,EAAE,KAAK,CAAC,QAAQ,EAAE,EAAE,GAAG,GAAG;AACpE,UAAI,IAAI,QAAQ,SAAS,GAAG;AAC3B,sBAAc,KAAK,OAAO;MAC3B;IACD;AACA,kBAAc,KAAK,MAAM;AACzB,WAAO,IAAI,KAAK,aAAa;EAC9B;EAEA,iBAAiB,EAAE,OAAO,OAAO,WAAW,SAAS,GAAwB;AAC5E,UAAM,UAAU,KAAK,aAAa,QAAQ;AAE1C,UAAM,eAAe,YAClB,iBAAiB,KAAK,eAAe,WAAW,EAAE,eAAe,KAAK,CAAC,CAAC,KACxE;AAEH,UAAM,WAAW,QAAQ,aAAa,KAAK,KAAK;AAEhD,WAAO,MAAM,OAAO,eAAe,KAAK,GAAG,QAAQ,GAAG,YAAY;EACnE;EAEA,eAAe,OAAgB,KAAqB;AACnD,UAAM,eAAe,MAAM,MAAM,OAAO,OAAO;AAE/C,UAAM,cAAc,OAAO,KAAK,YAAY,EAAE;MAAO,CAAC,YAAA;AA1JxD,YAAAA;AA2JG,mBAAI,OAAO,MAAM,YAAaA,OAAA,aAAa,OAAO,MAApB,gBAAAA,KAAuB,gBAAe;;IACrE;AAEA,UAAM,UAAU,YAAY;AAC5B,WAAO,IAAI,KAAK,YAAY,QAAQ,CAAC,SAAS,MAAM;AACnD,YAAM,MAAM,aAAa,OAAO;AAEhC,YAAM,QAAQ,IAAI,OAAO,KAAK,IAAI,MAAM,IAAI,WAAY,GAAG,GAAG;AAC9D,YAAM,MAAM,MAAM,IAAI,WAAW,KAAK,OAAO,gBAAgB,GAAG,CAAC,CAAC,MAAM,KAAK;AAE7E,UAAI,IAAI,UAAU,GAAG;AACpB,eAAO,CAAC,KAAK,IAAI,IAAI,IAAI,CAAC;MAC3B;AACA,aAAO,CAAC,GAAG;IACZ,CAAC,CAAC;EACH;EAEA,iBAAiB,EAAE,OAAO,KAAK,OAAO,WAAW,UAAU,MAAM,MAAM,GAAwB;AAC9F,UAAM,UAAU,KAAK,aAAa,QAAQ;AAE1C,UAAM,YAAY,MAAM,QAAQ,OAAO,IAAI;AAC3C,UAAM,cAAc,MAAM,QAAQ,OAAO,MAAM;AAC/C,UAAM,gBAAgB,MAAM,QAAQ,OAAO,YAAY;AACvD,UAAMC,SAAQ,cAAc,gBAAgB,SAAY;AACxD,UAAM,WAAW,MAAM,cAAc,MAAM,IAAI,WAAW,WAAW,CAAC,MAAM,MAAS,GACpF,IAAI,WAAW,aAAa,CAC7B,GAAGA,UAAS,OAAO,IAAI,WAAWA,MAAK,CAAC,EAAE;AAE1C,UAAM,SAAS,KAAK,eAAe,OAAO,GAAG;AAE7C,UAAM,UAAU,QAAQ,IAAI,KAAK,CAAC,IAAI,IAAI,QAAQ,GAAG,KAAK,eAAe,IAAI,CAAC,CAAC;AAE/E,UAAM,WAAW,KAAK,WAAW,KAAK;AAEtC,UAAM,eAAe,YAClB,iBAAiB,KAAK,eAAe,WAAW,EAAE,eAAe,CAAC,KAAK,CAAC,CAAC,KACzE;AAEH,UAAM,WAAW,QAAQ,aAAa,KAAK,KAAK;AAEhD,WAAO,MAAM,OAAO,UAAU,QAAQ,QAAQ,MAAM,GAAG,OAAO,GAAG,QAAQ,GAAG,QAAQ,GAAG,YAAY;EACpG;;;;;;;;;;;;EAaQ,eACP,QACA,EAAE,gBAAgB,MAAM,IAAiC,CAAC,GACpD;AACN,UAAM,aAAa,OAAO;AAE1B,UAAM,SAAS,OACb,QAAQ,CAAC,EAAE,MAAM,GAAG,MAAM;AAC1B,YAAM,QAAoB,CAAC;AAE3B,UAAI,GAAG,OAAO,IAAI,OAAO,KAAK,MAAM,kBAAkB;AACrD,cAAM,KAAK,IAAI,WAAW,MAAM,UAAU,CAAC;MAC5C,WAAW,GAAG,OAAO,IAAI,OAAO,KAAK,GAAG,OAAO,GAAG,GAAG;AACpD,cAAM,QAAQ,GAAG,OAAO,IAAI,OAAO,IAAI,MAAM,MAAM;AAEnD,YAAI,eAAe;AAClB,gBAAM;YACL,IAAI;cACH,MAAM,YAAY,IAAI,CAAC,MAAM;AAC5B,oBAAI,GAAG,GAAG,QAAQ,GAAG;AACpB,yBAAO,IAAI,WAAW,KAAK,OAAO,gBAAgB,CAAC,CAAC;gBACrD;AACA,uBAAO;cACR,CAAC;YACF;UACD;QACD,OAAO;AACN,gBAAM,KAAK,KAAK;QACjB;AAEA,YAAI,GAAG,OAAO,IAAI,OAAO,GAAG;AAC3B,gBAAM,KAAK,UAAU,IAAI,WAAW,MAAM,UAAU,CAAC,EAAE;QACxD;MACD,WAAW,GAAG,OAAO,MAAM,GAAG;AAC7B,YAAI,eAAe;AAClB,gBAAM,KAAK,IAAI,WAAW,KAAK,OAAO,gBAAgB,KAAK,CAAC,CAAC;QAC9D,OAAO;AACN,gBAAM,KAAK,KAAK;QACjB;MACD;AAEA,UAAI,IAAI,aAAa,GAAG;AACvB,cAAM,KAAK,OAAO;MACnB;AAEA,aAAO;IACR,CAAC;AAEF,WAAO,IAAI,KAAK,MAAM;EACvB;EAEQ,WAAW,OAA0D;AAC5E,QAAI,CAAC,SAAS,MAAM,WAAW,GAAG;AACjC,aAAO;IACR;AAEA,UAAM,aAAoB,CAAC;AAE3B,eAAW,CAACC,QAAO,QAAQ,KAAK,MAAM,QAAQ,GAAG;AAChD,UAAIA,WAAU,GAAG;AAChB,mBAAW,KAAK,MAAM;MACvB;AACA,YAAM,QAAQ,SAAS;AACvB,YAAM,aAAa,SAAS,UAAU,gBAAgB;AAEtD,UAAI,GAAG,OAAO,OAAO,GAAG;AACvB,cAAM,YAAY,MAAM,QAAQ,OAAO,IAAI;AAC3C,cAAM,cAAc,MAAM,QAAQ,OAAO,MAAM;AAC/C,cAAM,gBAAgB,MAAM,QAAQ,OAAO,YAAY;AACvD,cAAMD,SAAQ,cAAc,gBAAgB,SAAY,SAAS;AACjE,mBAAW;UACV,MAAM,IAAI,IAAI,SAAS,QAAQ,CAAC,QAAQ,UAAU,IACjD,cAAc,MAAM,IAAI,WAAW,WAAW,CAAC,MAAM,MACtD,GAAG,IAAI,WAAW,aAAa,CAAC,GAAGA,UAAS,OAAO,IAAI,WAAWA,MAAK,CAAC,EAAE,OAAO,SAAS,EAAE;QAC7F;MACD,WAAW,GAAG,OAAO,IAAI,GAAG;AAC3B,cAAM,WAAW,MAAM,cAAc,EAAE;AACvC,cAAM,aAAa,MAAM,cAAc,EAAE;AACzC,cAAM,eAAe,MAAM,cAAc,EAAE;AAC3C,cAAMA,SAAQ,aAAa,eAAe,SAAY,SAAS;AAC/D,mBAAW;UACV,MAAM,IAAI,IAAI,SAAS,QAAQ,CAAC,QAAQ,UAAU,IACjD,aAAa,MAAM,IAAI,WAAW,UAAU,CAAC,MAAM,MACpD,GAAG,IAAI,WAAW,YAAY,CAAC,GAAGA,UAAS,OAAO,IAAI,WAAWA,MAAK,CAAC,EAAE,OAAO,SAAS,EAAE;QAC5F;MACD,OAAO;AACN,mBAAW;UACV,MAAM,IAAI,IAAI,SAAS,QAAQ,CAAC,QAAQ,UAAU,IAAI,KAAK,OAAO,SAAS,EAAE;QAC9E;MACD;AACA,UAAIC,SAAQ,MAAM,SAAS,GAAG;AAC7B,mBAAW,KAAK,MAAM;MACvB;IACD;AAEA,WAAO,IAAI,KAAK,UAAU;EAC3B;EAEQ,eACP,OACoD;AACpD,QAAI,GAAG,OAAO,KAAK,KAAK,MAAM,MAAM,OAAO,YAAY,MAAM,MAAM,MAAM,OAAO,IAAI,GAAG;AACtF,UAAI,WAAW,MAAM,IAAI,WAAW,MAAM,MAAM,OAAO,YAAY,CAAC,CAAC;AACrE,UAAI,MAAM,MAAM,OAAO,MAAM,GAAG;AAC/B,mBAAW,MAAM,IAAI,WAAW,MAAM,MAAM,OAAO,MAAM,CAAE,CAAC,IAAI,QAAQ;MACzE;AACA,aAAO,MAAM,QAAQ,IAAI,IAAI,WAAW,MAAM,MAAM,OAAO,IAAI,CAAC,CAAC;IAClE;AAEA,WAAO;EACR;EAEA,iBACC;IACC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACD,GACM;AACN,UAAM,aAAa,cAAc,oBAA8B,MAAM;AACrE,eAAW,KAAK,YAAY;AAC3B,UACC,GAAG,EAAE,OAAO,MAAM,KACf,aAAa,EAAE,MAAM,KAAK,OACvB,GAAG,OAAO,QAAQ,IACpB,MAAM,EAAE,QACR,GAAG,OAAO,UAAU,IACpB,MAAM,cAAc,EAAE,OACtB,GAAG,OAAO,GAAG,IACb,SACA,aAAa,KAAK,MACnB,EAAE,CAACC,WACL,+BAAO;QAAK,CAAC,EAAE,OAAAF,OAAM,MACpBA,YAAWE,OAAM,MAAM,OAAO,OAAO,IAAI,aAAaA,MAAK,IAAIA,OAAM,MAAM,OAAO,QAAQ;SACxF,EAAE,MAAM,KAAK,GAChB;AACD,cAAM,YAAY,aAAa,EAAE,MAAM,KAAK;AAC5C,cAAM,IAAI;UACT,SACC,EAAE,KAAK,KAAK,IAAI,CACjB,gCAAgC,SAAS,MAAM,EAAE,MAAM,IAAI,qBAAqB,SAAS;QAC1F;MACD;IACD;AAEA,UAAM,gBAAgB,CAAC,SAAS,MAAM,WAAW;AAEjD,UAAM,UAAU,KAAK,aAAa,QAAQ;AAE1C,QAAI;AACJ,QAAI,UAAU;AACb,oBAAc,aAAa,OAAO,iBAAiB,oBAAoB,IAAI,KAAK,SAAS,IAAI,OAAO,CAAC;IACtG;AAEA,UAAM,YAAY,KAAK,eAAe,YAAY,EAAE,cAAc,CAAC;AAEnE,UAAM,WAAW,KAAK,eAAe,KAAK;AAE1C,UAAM,WAAW,KAAK,WAAW,KAAK;AAEtC,UAAM,WAAW,QAAQ,aAAa,KAAK,KAAK;AAEhD,UAAM,YAAY,SAAS,cAAc,MAAM,KAAK;AAEpD,QAAI;AACJ,QAAI,WAAW,QAAQ,SAAS,GAAG;AAClC,mBAAa,gBAAgB,IAAI,KAAK,SAAS,OAAO,CAAC;IACxD;AAEA,QAAI;AACJ,QAAI,WAAW,QAAQ,SAAS,GAAG;AAClC,mBAAa,gBAAgB,IAAI,KAAK,SAAS,OAAO,CAAC;IACxD;AAEA,UAAM,WAAW,OAAO,UAAU,YAAa,OAAO,UAAU,YAAY,SAAS,IAClF,aAAa,KAAK,KAClB;AAEH,UAAM,YAAY,SAAS,cAAc,MAAM,KAAK;AAEpD,UAAM,mBAAmB,IAAI,MAAM;AACnC,QAAI,eAAe;AAClB,YAAM,YAAY,WAAW,IAAI,IAAI,cAAc,QAAQ,CAAC;AAC5D,UAAI,cAAc,OAAO,IAAI;AAC5B,kBAAU;UACT,UACC,IAAI;YACH,MAAM,QAAQ,cAAc,OAAO,EAAE,IAAI,cAAc,OAAO,KAAK,CAAC,cAAc,OAAO,EAAE;YAC3F;UACD,CACD;QACD;MACD;AACA,UAAI,cAAc,OAAO,QAAQ;AAChC,kBAAU,OAAO,aAAa;MAC/B,WAAW,cAAc,OAAO,YAAY;AAC3C,kBAAU,OAAO,iBAAiB;MACnC;AACA,uBAAiB,OAAO,SAAS;IAClC;AACA,UAAM,aACL,MAAM,OAAO,SAAS,WAAW,IAAI,SAAS,SAAS,QAAQ,GAAG,QAAQ,GAAG,QAAQ,GAAG,UAAU,GAAG,SAAS,GAAG,UAAU,GAAG,QAAQ,GAAG,SAAS,GAAG,gBAAgB;AAEtK,QAAI,aAAa,SAAS,GAAG;AAC5B,aAAO,KAAK,mBAAmB,YAAY,YAAY;IACxD;AAEA,WAAO;EACR;EAEA,mBAAmB,YAAiB,cAAmD;AACtF,UAAM,CAAC,aAAa,GAAG,IAAI,IAAI;AAE/B,QAAI,CAAC,aAAa;AACjB,YAAM,IAAI,MAAM,kDAAkD;IACnE;AAEA,QAAI,KAAK,WAAW,GAAG;AACtB,aAAO,KAAK,uBAAuB,EAAE,YAAY,YAAY,CAAC;IAC/D;AAGA,WAAO,KAAK;MACX,KAAK,uBAAuB,EAAE,YAAY,YAAY,CAAC;MACvD;IACD;EACD;EAEA,uBAAuB;IACtB;IACA,aAAa,EAAE,MAAM,OAAO,aAAa,OAAO,SAAS,OAAO;EACjE,GAAkF;AACjF,UAAM,YAAY,OAAO,WAAW,OAAO,CAAC;AAC5C,UAAM,aAAa,OAAO,YAAY,OAAO,CAAC;AAE9C,QAAI;AACJ,QAAI,WAAW,QAAQ,SAAS,GAAG;AAClC,YAAM,gBAAyC,CAAC;AAIhD,iBAAW,iBAAiB,SAAS;AACpC,YAAI,GAAG,eAAe,QAAQ,GAAG;AAChC,wBAAc,KAAK,IAAI,WAAW,cAAc,IAAI,CAAC;QACtD,WAAW,GAAG,eAAe,GAAG,GAAG;AAClC,mBAAS,IAAI,GAAG,IAAI,cAAc,YAAY,QAAQ,KAAK;AAC1D,kBAAM,QAAQ,cAAc,YAAY,CAAC;AAEzC,gBAAI,GAAG,OAAO,QAAQ,GAAG;AACxB,4BAAc,YAAY,CAAC,IAAI,IAAI,WAAW,MAAM,IAAI;YACzD;UACD;AAEA,wBAAc,KAAK,MAAM,aAAa,EAAE;QACzC,OAAO;AACN,wBAAc,KAAK,MAAM,aAAa,EAAE;QACzC;MACD;AAEA,mBAAa,gBAAgB,IAAI,KAAK,eAAe,OAAO,CAAC;IAC9D;AAEA,UAAM,WAAW,OAAO,UAAU,YAAa,OAAO,UAAU,YAAY,SAAS,IAClF,aAAa,KAAK,KAClB;AAEH,UAAM,gBAAgB,IAAI,IAAI,GAAG,IAAI,IAAI,QAAQ,SAAS,EAAE,EAAE;AAE9D,UAAM,YAAY,SAAS,cAAc,MAAM,KAAK;AAEpD,WAAO,MAAM,SAAS,GAAG,aAAa,GAAG,UAAU,GAAG,UAAU,GAAG,QAAQ,GAAG,SAAS;EACxF;EAEA,iBACC,EAAE,OAAO,QAAQ,gBAAgB,YAAY,WAAW,UAAU,QAAQ,uBAAuB,GAC3F;AACN,UAAM,gBAA8C,CAAC;AACrD,UAAM,UAAoC,MAAM,MAAM,OAAO,OAAO;AAEpE,UAAM,aAAmC,OAAO,QAAQ,OAAO,EAAE,OAAO,CAAC,CAAC,GAAG,GAAG,MAAM,CAAC,IAAI,oBAAoB,CAAC;AAEhH,UAAM,cAAc,WAAW;MAC9B,CAAC,CAAC,EAAE,MAAM,MAAM,IAAI,WAAW,KAAK,OAAO,gBAAgB,MAAM,CAAC;IACnE;AAEA,QAAI,QAAQ;AACX,YAAMC,UAAS;AAEf,UAAI,GAAGA,SAAQ,GAAG,GAAG;AACpB,sBAAc,KAAKA,OAAM;MAC1B,OAAO;AACN,sBAAc,KAAKA,QAAO,OAAO,CAAC;MACnC;IACD,OAAO;AACN,YAAM,SAAS;AACf,oBAAc,KAAK,IAAI,IAAI,SAAS,CAAC;AAErC,iBAAW,CAAC,YAAY,KAAK,KAAK,OAAO,QAAQ,GAAG;AACnD,cAAM,YAAgC,CAAC;AACvC,mBAAW,CAAC,WAAW,GAAG,KAAK,YAAY;AAC1C,gBAAM,WAAW,MAAM,SAAS;AAChC,cAAI,aAAa,UAAc,GAAG,UAAU,KAAK,KAAK,SAAS,UAAU,QAAY;AAEpF,gBAAI,IAAI,cAAc,QAAW;AAChC,oBAAM,kBAAkB,IAAI,UAAU;AACtC,oBAAM,eAAe,GAAG,iBAAiB,GAAG,IAAI,kBAAkB,IAAI,MAAM,iBAAiB,GAAG;AAChG,wBAAU,KAAK,YAAY;YAE5B,WAAW,CAAC,IAAI,WAAW,IAAI,eAAe,QAAW;AACxD,oBAAM,mBAAmB,IAAI,WAAW;AACxC,oBAAM,WAAW,GAAG,kBAAkB,GAAG,IAAI,mBAAmB,IAAI,MAAM,kBAAkB,GAAG;AAC/F,wBAAU,KAAK,QAAQ;YACxB,OAAO;AACN,wBAAU,KAAK,YAAY;YAC5B;UACD,OAAO;AACN,sBAAU,KAAK,QAAQ;UACxB;QACD;AAEA,sBAAc,KAAK,SAAS;AAC5B,YAAI,aAAa,OAAO,SAAS,GAAG;AACnC,wBAAc,KAAK,OAAO;QAC3B;MACD;IACD;AAEA,UAAM,UAAU,KAAK,aAAa,QAAQ;AAE1C,UAAM,YAAY,IAAI,KAAK,aAAa;AAExC,UAAM,eAAe,YAClB,iBAAiB,KAAK,eAAe,WAAW,EAAE,eAAe,KAAK,CAAC,CAAC,KACxE;AAEH,UAAM,gBAAgB,aAAa,mBAAmB,UAAU,KAAK;AAErE,UAAM,gBAAgB,2BAA2B,OAAO,gCAAgC;AAExF,WAAO,MAAM,OAAO,eAAe,KAAK,IAAI,WAAW,IAAI,aAAa,GAAG,SAAS,GAAG,aAAa,GAAG,YAAY;EACpH;EAEA,kCACC,EAAE,MAAM,cAAc,WAAW,GAC3B;AACN,UAAM,kBAAkB,eAAe,qBAAqB;AAC5D,UAAM,gBAAgB,aAAa,qBAAqB;AAExD,WAAO,+BAA+B,eAAe,IAAI,IAAI,GAAG,aAAa;EAC9E;EAEA,cAAc,SAAkE;AAC/E,QAAI,GAAG,SAAS,OAAO,KAAK,GAAG,SAAS,MAAM,GAAG;AAChD,aAAO;IACR,WAAW,GAAG,SAAS,SAAS,GAAG;AAClC,aAAO;IACR,WAAW,GAAG,SAAS,MAAM,GAAG;AAC/B,aAAO;IACR,WAAW,GAAG,SAAS,WAAW,KAAK,GAAG,SAAS,iBAAiB,GAAG;AACtE,aAAO;IACR,WAAW,GAAG,SAAS,MAAM,KAAK,GAAG,SAAS,YAAY,GAAG;AAC5D,aAAO;IACR,WAAW,GAAG,SAAS,MAAM,GAAG;AAC/B,aAAO;IACR,OAAO;AACN,aAAO;IACR;EACD;EAEA,WAAWC,MAAU,cAAwD;AAC5E,WAAOA,KAAI,QAAQ;MAClB,QAAQ,KAAK;MACb,YAAY,KAAK;MACjB,aAAa,KAAK;MAClB,cAAc,KAAK;MACnB,eAAe,KAAK;MACpB;IACD,CAAC;EACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAohBA,8BAA8B;IAC7B;IACA;IACA;IACA;IACA;IACA,aAAa;IACb;IACA;IACA;EACD,GAUkD;AACjD,QAAI,YAAwE,CAAC;AAC7E,QAAI,OAAO,QAAQ,UAAkD,CAAC,GAAG;AACzE,UAAM,QAA8B,CAAC;AAErC,QAAI,WAAW,MAAM;AACpB,YAAM,mBAAmB,OAAO,QAAQ,YAAY,OAAO;AAC3D,kBAAY,iBAAiB,IAAI,CAChC,CAAC,KAAK,KAAK,OACN;QACL,OAAO,MAAM;QACb,OAAO;QACP,OAAO,mBAAmB,OAAmB,UAAU;QACvD,oBAAoB;QACpB,QAAQ;QACR,WAAW,CAAC;MACb,EAAE;IACH,OAAO;AACN,YAAM,iBAAiB,OAAO;QAC7B,OAAO,QAAQ,YAAY,OAAO,EAAE,IAAI,CACvC,CAAC,KAAK,KAAK,MACP,CAAC,KAAK,mBAAmB,OAAO,UAAU,CAAC,CAAC;MAClD;AAEA,UAAI,OAAO,OAAO;AACjB,cAAM,WAAW,OAAO,OAAO,UAAU,aACtC,OAAO,MAAM,gBAAgB,aAAa,CAAC,IAC3C,OAAO;AACV,gBAAQ,YAAY,uBAAuB,UAAU,UAAU;MAChE;AAEA,YAAM,kBAAsE,CAAC;AAC7E,UAAI,kBAA4B,CAAC;AAGjC,UAAI,OAAO,SAAS;AACnB,YAAI,gBAAgB;AAEpB,mBAAW,CAAC,OAAO,KAAK,KAAK,OAAO,QAAQ,OAAO,OAAO,GAAG;AAC5D,cAAI,UAAU,QAAW;AACxB;UACD;AAEA,cAAI,SAAS,YAAY,SAAS;AACjC,gBAAI,CAAC,iBAAiB,UAAU,MAAM;AACrC,8BAAgB;YACjB;AACA,4BAAgB,KAAK,KAAK;UAC3B;QACD;AAEA,YAAI,gBAAgB,SAAS,GAAG;AAC/B,4BAAkB,gBACf,gBAAgB,OAAO,CAAC,MAAA;AAnrChC,gBAAAL;AAmrCsC,qBAAAA,OAAA,OAAO,YAAP,gBAAAA,KAAiB,QAAO;WAAI,IAC1D,OAAO,KAAK,YAAY,OAAO,EAAE,OAAO,CAAC,QAAQ,CAAC,gBAAgB,SAAS,GAAG,CAAC;QACnF;MACD,OAAO;AAEN,0BAAkB,OAAO,KAAK,YAAY,OAAO;MAClD;AAEA,iBAAW,SAAS,iBAAiB;AACpC,cAAM,SAAS,YAAY,QAAQ,KAAK;AACxC,wBAAgB,KAAK,EAAE,OAAO,OAAO,OAAO,OAAO,CAAC;MACrD;AAEA,UAAI,oBAIE,CAAC;AAGP,UAAI,OAAO,MAAM;AAChB,4BAAoB,OAAO,QAAQ,OAAO,IAAI,EAC5C,OAAO,CAAC,UAAoE,CAAC,CAAC,MAAM,CAAC,CAAC,EACtF,IAAI,CAAC,CAAC,OAAO,WAAW,OAAO,EAAE,OAAO,aAAa,UAAU,YAAY,UAAU,KAAK,EAAG,EAAE;MAClG;AAEA,UAAI;AAGJ,UAAI,OAAO,QAAQ;AAClB,iBAAS,OAAO,OAAO,WAAW,aAC/B,OAAO,OAAO,gBAAgB,EAAE,IAAI,CAAC,IACrC,OAAO;AACV,mBAAW,CAAC,OAAO,KAAK,KAAK,OAAO,QAAQ,MAAM,GAAG;AACpD,0BAAgB,KAAK;YACpB;YACA,OAAO,8BAA8B,OAAO,UAAU;UACvD,CAAC;QACF;MACD;AAIA,iBAAW,EAAE,OAAO,MAAM,KAAK,iBAAiB;AAC/C,kBAAU,KAAK;UACd,OAAO,GAAG,OAAO,IAAI,OAAO,IAAI,MAAM,aAAa,YAAY,QAAQ,KAAK,EAAG;UAC/E;UACA,OAAO,GAAG,OAAO,MAAM,IAAI,mBAAmB,OAAO,UAAU,IAAI;UACnE,oBAAoB;UACpB,QAAQ;UACR,WAAW,CAAC;QACb,CAAC;MACF;AAEA,UAAI,cAAc,OAAO,OAAO,YAAY,aACzC,OAAO,QAAQ,gBAAgB,oBAAoB,CAAC,IACpD,OAAO,WAAW,CAAC;AACtB,UAAI,CAAC,MAAM,QAAQ,WAAW,GAAG;AAChC,sBAAc,CAAC,WAAW;MAC3B;AACA,gBAAU,YAAY,IAAI,CAAC,iBAAiB;AAC3C,YAAI,GAAG,cAAc,MAAM,GAAG;AAC7B,iBAAO,mBAAmB,cAAc,UAAU;QACnD;AACA,eAAO,uBAAuB,cAAc,UAAU;MACvD,CAAC;AAED,cAAQ,OAAO;AACf,eAAS,OAAO;AAGhB,iBACO;QACL,OAAO;QACP,aAAa;QACb;MACD,KAAK,mBACJ;AACD,cAAM,qBAAqB,kBAAkB,QAAQ,eAAe,QAAQ;AAC5E,cAAM,oBAAoB,mBAAmB,SAAS,eAAe;AACrE,cAAM,sBAAsB,cAAc,iBAAiB;AAC3D,cAAM,qBAAqB,GAAG,UAAU,IAAI,qBAAqB;AACjE,cAAMM,UAAS;UACd,GAAG,mBAAmB,OAAO;YAAI,CAACC,QAAO,MACxC;cACC,mBAAmB,mBAAmB,WAAW,CAAC,GAAI,kBAAkB;cACxE,mBAAmBA,QAAO,UAAU;YACrC;UACD;QACD;AACA,cAAM,gBAAgB,KAAK,8BAA8B;UACxD;UACA;UACA;UACA,OAAO,WAAW,mBAAmB;UACrC,aAAa,OAAO,mBAAmB;UACvC,aAAa,GAAG,UAAU,GAAG,IACzB,gCAAgC,OAChC,EAAE,OAAO,EAAE,IACX,EAAE,GAAG,6BAA6B,OAAO,EAAE,IAC5C;UACH,YAAY;UACZ,QAAAD;UACA,qBAAqB;QACtB,CAAC;AACD,cAAM,QAAQ,MAAM,IAAI,WAAW,kBAAkB,CAAC,IAAI,IAAI,WAAW,MAAM,CAAC,GAAG,GAAG,qBAAqB;AAC3G,cAAM,KAAK;UACV,IAAI;UACJ,OAAO,IAAI,SAAS,cAAc,KAAY,CAAC,GAAG,kBAAkB;UACpE,OAAO;UACP,UAAU;UACV,SAAS;QACV,CAAC;AACD,kBAAU,KAAK;UACd,OAAO;UACP,OAAO;UACP;UACA,oBAAoB;UACpB,QAAQ;UACR,WAAW,cAAc;QAC1B,CAAC;MACF;IACD;AAEA,QAAI,UAAU,WAAW,GAAG;AAC3B,YAAM,IAAI,aAAa,EAAE,SAAS,iCAAiC,YAAY,MAAM,OAAO,UAAU,KAAK,CAAC;IAC7G;AAEA,QAAI;AAEJ,YAAQ,IAAI,QAAQ,KAAK;AAEzB,QAAI,qBAAqB;AACxB,UAAI,QAAQ,uBACX,IAAI;QACH,UAAU;UAAI,CAAC,EAAE,OAAAC,QAAO,OAAO,OAAO,MACrC,SACG,MAAM,IAAI,WAAW,GAAG,UAAU,IAAI,KAAK,EAAE,CAAC,IAAI,IAAI,WAAW,MAAM,CAAC,KACxE,GAAGA,QAAO,IAAI,OAAO,IACrBA,OAAM,MACNA;QACJ;QACA;MACD,CACD;AACA,UAAI,GAAG,qBAAqB,IAAI,GAAG;AAClC,gBAAQ,wBAAwB,KAAK,GACpC,QAAQ,SAAS,IAAI,gBAAgB,IAAI,KAAK,SAAS,OAAO,CAAC,KAAK,MACrE;MAED;AACA,YAAM,kBAAkB,CAAC;QACxB,OAAO;QACP,OAAO;QACP,OAAO,MAAM,GAAG,MAAM;QACtB,QAAQ;QACR,oBAAoB,YAAY;QAChC;MACD,CAAC;AAED,YAAM,gBAAgB,UAAU,UAAa,WAAW,UAAa,QAAQ,SAAS;AAEtF,UAAI,eAAe;AAClB,iBAAS,KAAK,iBAAiB;UAC9B,OAAO,aAAa,OAAO,UAAU;UACrC,QAAQ,CAAC;UACT,YAAY,CAAC;YACZ,MAAM,CAAC;YACP,OAAO,IAAI,IAAI,GAAG;UACnB,CAAC;UACD;UACA;UACA;UACA;UACA,cAAc,CAAC;QAChB,CAAC;AAED,gBAAQ;AACR,gBAAQ;AACR,iBAAS;AACT,kBAAU,CAAC;MACZ,OAAO;AACN,iBAAS,aAAa,OAAO,UAAU;MACxC;AAEA,eAAS,KAAK,iBAAiB;QAC9B,OAAO,GAAG,QAAQ,OAAO,IAAI,SAAS,IAAI,SAAS,QAAQ,CAAC,GAAG,UAAU;QACzE,QAAQ,CAAC;QACT,YAAY,gBAAgB,IAAI,CAAC,EAAE,OAAAA,OAAM,OAAO;UAC/C,MAAM,CAAC;UACP,OAAO,GAAGA,QAAO,MAAM,IAAI,mBAAmBA,QAAO,UAAU,IAAIA;QACpE,EAAE;QACF;QACA;QACA;QACA;QACA;QACA,cAAc,CAAC;MAChB,CAAC;IACF,OAAO;AACN,eAAS,KAAK,iBAAiB;QAC9B,OAAO,aAAa,OAAO,UAAU;QACrC,QAAQ,CAAC;QACT,YAAY,UAAU,IAAI,CAAC,EAAE,MAAM,OAAO;UACzC,MAAM,CAAC;UACP,OAAO,GAAG,OAAO,MAAM,IAAI,mBAAmB,OAAO,UAAU,IAAI;QACpE,EAAE;QACF;QACA;QACA;QACA;QACA;QACA,cAAc,CAAC;MAChB,CAAC;IACF;AAEA,WAAO;MACN,YAAY,YAAY;MACxB,KAAK;MACL;IACD;EACD;AACD;AAl1CC,cADY,WACKP,KAAsB;;;AC/DxC,IAAAQ;AAIkBA,MAAA;AADX,IAAe,oBAAf,MAAsF;;EAS5F,oBAAgC;AAC/B,WAAO,KAAK,EAAE;EACf;AAGD;AAbC,cADqB,mBACJA,KAAsB;;;ACJxC,IAAAC;AA8DkBA,MAAA;AAJX,IAAM,kBAAN,MAGL;EAWD,YACC,QASC;AAlBM;AACA;AACA;AACA,oCAAuB,CAAC;AACxB;AAwBA;AATP,SAAK,SAAS,OAAO;AACrB,SAAK,UAAU,OAAO;AACtB,SAAK,UAAU,OAAO;AACtB,QAAI,OAAO,UAAU;AACpB,WAAK,WAAW,OAAO;IACxB;AACA,SAAK,WAAW,OAAO;EACxB;;EAIA,SAAS,OAAuB;AAC/B,SAAK,YAAY;AACjB,WAAO;EACR;;;;;;;EAQA,KACC,QASC;AACD,UAAM,kBAAkB,CAAC,CAAC,KAAK;AAC/B,UAAM,MAAM;AAEZ,QAAI;AACJ,QAAI,KAAK,QAAQ;AAChB,eAAS,KAAK;IACf,WAAW,GAAG,KAAK,QAAQ,GAAG;AAE7B,eAAS,OAAO;QACf,OAAO,KAAK,IAAI,EAAE,cAAc,EAAE,IAAI,CACrC,QACI,CAAC,KAAK,IAAI,GAAkC,CAAsC,CAAC;MACzF;IACD,WAAW,GAAG,KAAK,UAAU,GAAG;AAC/B,eAAS,IAAI,cAAc,EAAE;IAC9B,WAAW,GAAG,KAAK,GAAG,GAAG;AACxB,eAAS,CAAC;IACX,OAAO;AACN,eAAS,gBAAyB,GAAG;IACtC;AAEA,WAAQ,IAAI,aAAa;MACxB,OAAO;MACP;MACA;MACA,SAAS,KAAK;MACd,SAAS,KAAK;MACd,UAAU,KAAK;MACf,UAAU,KAAK;IAChB,CAAC,EAAE,SAAS,KAAK,SAAS;EAC3B;AACD;AArFC,cAJY,iBAIKA,KAAsB;AA9DxC,IAAAA,MAAAC;AAqJO,IAAe,2BAAf,eAWGA,MAAA,mBACiBD,OAAA,YADjBC,KAA4C;EAuBrD,YACC,EAAE,OAAO,QAAQ,iBAAiB,SAAS,SAAS,UAAU,SAAS,GAWtE;AACD,UAAM;AAjCW;AAaR;AACA;AACF;AACA;AACE;AACA;AAyIV;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAAW,KAAK,WAAW,MAAM;AA6BjC;;;;;;;;;;;;;;;;;;;;;;;;;;;qCAAY,KAAK,WAAW,OAAO;AA6BnC;;;;;;;;;;;;;;;;;;;;;;;;;;;qCAAY,KAAK,WAAW,OAAO;AA6BnC;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAAW,KAAK,WAAW,MAAM;AA2DjC;;;;;;;;;;;;;;;;;;;;;;;;;iCAAQ,KAAK,kBAAkB,SAAS,KAAK;AA2B7C;;;;;;;;;;;;;;;;;;;;;;;;;oCAAW,KAAK,kBAAkB,SAAS,IAAI;AA2B/C;;;;;;;;;;;;;;;;;;;;;;;;;qCAAY,KAAK,kBAAkB,aAAa,KAAK;AA0CrD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wCAAe,KAAK,kBAAkB,aAAa,IAAI;AA2BvD;;;;;;;;;;;;;;;;;;;;;;;;;kCAAS,KAAK,kBAAkB,UAAU,KAAK;AA0C/C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;qCAAY,KAAK,kBAAkB,UAAU,IAAI;AAhbhD,SAAK,SAAS;MACb;MACA;MACA,QAAQ,EAAE,GAAG,OAAO;MACpB;MACA,cAAc,CAAC;IAChB;AACA,SAAK,kBAAkB;AACvB,SAAK,UAAU;AACf,SAAK,UAAU;AACf,SAAK,IAAI;MACR,gBAAgB;IACjB;AACA,SAAK,YAAY,iBAAiB,KAAK;AACvC,SAAK,sBAAsB,OAAO,KAAK,cAAc,WAAW,EAAE,CAAC,KAAK,SAAS,GAAG,KAAK,IAAI,CAAC;EAC/F;EAEQ,WACP,UAC4C;AAC5C,WAAQ,CACP,OACA,OACI;AA5NP,UAAAD;AA6NG,YAAM,gBAAgB,KAAK;AAC3B,YAAM,YAAY,iBAAiB,KAAK;AAExC,UAAI,OAAO,cAAc,cAAYA,OAAA,KAAK,OAAO,UAAZ,gBAAAA,KAAmB,KAAK,CAAC,SAAS,KAAK,UAAU,aAAY;AACjG,cAAM,IAAI,MAAM,UAAU,SAAS,iCAAiC;MACrE;AAEA,UAAI,CAAC,KAAK,iBAAiB;AAE1B,YAAI,OAAO,KAAK,KAAK,mBAAmB,EAAE,WAAW,KAAK,OAAO,kBAAkB,UAAU;AAC5F,eAAK,OAAO,SAAS;YACpB,CAAC,aAAa,GAAG,KAAK,OAAO;UAC9B;QACD;AACA,YAAI,OAAO,cAAc,YAAY,CAAC,GAAG,OAAO,GAAG,GAAG;AACrD,gBAAM,YAAY,GAAG,OAAO,QAAQ,IACjC,MAAM,EAAE,iBACR,GAAG,OAAO,IAAI,IACd,MAAM,cAAc,EAAE,iBACtB,MAAM,MAAM,OAAO,OAAO;AAC7B,eAAK,OAAO,OAAO,SAAS,IAAI;QACjC;MACD;AAEA,UAAI,OAAO,OAAO,YAAY;AAC7B,aAAK;UACJ,IAAI;YACH,KAAK,OAAO;YACZ,IAAI,sBAAsB,EAAE,oBAAoB,OAAO,aAAa,MAAM,CAAC;UAC5E;QACD;MACD;AAEA,UAAI,CAAC,KAAK,OAAO,OAAO;AACvB,aAAK,OAAO,QAAQ,CAAC;MACtB;AAEA,WAAK,OAAO,MAAM,KAAK,EAAE,IAAI,OAAO,UAAU,OAAO,UAAU,CAAC;AAEhE,UAAI,OAAO,cAAc,UAAU;AAClC,gBAAQ,UAAU;UACjB,KAAK,QAAQ;AACZ,iBAAK,oBAAoB,SAAS,IAAI;AACtC;UACD;UACA,KAAK,SAAS;AACb,iBAAK,sBAAsB,OAAO;cACjC,OAAO,QAAQ,KAAK,mBAAmB,EAAE,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,KAAK,CAAC;YACrE;AACA,iBAAK,oBAAoB,SAAS,IAAI;AACtC;UACD;UACA,KAAK,SAAS;AACb,iBAAK,oBAAoB,SAAS,IAAI;AACtC;UACD;UACA,KAAK,QAAQ;AACZ,iBAAK,sBAAsB,OAAO;cACjC,OAAO,QAAQ,KAAK,mBAAmB,EAAE,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,KAAK,CAAC;YACrE;AACA,iBAAK,oBAAoB,SAAS,IAAI;AACtC;UACD;QACD;MACD;AAEA,aAAO;IACR;EACD;EAsHQ,kBACP,MACA,OAUC;AACD,WAAO,CAAC,mBAAmB;AAC1B,YAAM,cAAe,OAAO,mBAAmB,aAC5C,eAAe,kBAAkB,CAAC,IAClC;AAKH,UAAI,CAAC,aAAa,KAAK,kBAAkB,GAAG,YAAY,kBAAkB,CAAC,GAAG;AAC7E,cAAM,IAAI;UACT;QACD;MACD;AAEA,WAAK,OAAO,aAAa,KAAK,EAAE,MAAM,OAAO,YAAY,CAAC;AAC1D,aAAO;IACR;EACD;;EAmMA,gBAAgB,cAKd;AACD,SAAK,OAAO,aAAa,KAAK,GAAG,YAAY;AAC7C,WAAO;EACR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA+BA,MACC,OAC2C;AAC3C,QAAI,OAAO,UAAU,YAAY;AAChC,cAAQ;QACP,IAAI;UACH,KAAK,OAAO;UACZ,IAAI,sBAAsB,EAAE,oBAAoB,OAAO,aAAa,MAAM,CAAC;QAC5E;MACD;IACD;AACA,SAAK,OAAO,QAAQ;AACpB,WAAO;EACR;;;;;;;;;;;;;;;;;;;;;;;EAwBA,OACC,QAC4C;AAC5C,QAAI,OAAO,WAAW,YAAY;AACjC,eAAS;QACR,IAAI;UACH,KAAK,OAAO;UACZ,IAAI,sBAAsB,EAAE,oBAAoB,OAAO,aAAa,MAAM,CAAC;QAC5E;MACD;IACD;AACA,SAAK,OAAO,SAAS;AACrB,WAAO;EACR;EAyBA,WACI,SAG0C;AAC7C,QAAI,OAAO,QAAQ,CAAC,MAAM,YAAY;AACrC,YAAM,UAAU,QAAQ,CAAC;QACxB,IAAI;UACH,KAAK,OAAO;UACZ,IAAI,sBAAsB,EAAE,oBAAoB,SAAS,aAAa,MAAM,CAAC;QAC9E;MACD;AACA,WAAK,OAAO,UAAU,MAAM,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO;IAClE,OAAO;AACN,WAAK,OAAO,UAAU;IACvB;AACA,WAAO;EACR;EA8BA,WACI,SAG0C;AAC7C,QAAI,OAAO,QAAQ,CAAC,MAAM,YAAY;AACrC,YAAM,UAAU,QAAQ,CAAC;QACxB,IAAI;UACH,KAAK,OAAO;UACZ,IAAI,sBAAsB,EAAE,oBAAoB,SAAS,aAAa,MAAM,CAAC;QAC9E;MACD;AAEA,YAAM,eAAe,MAAM,QAAQ,OAAO,IAAI,UAAU,CAAC,OAAO;AAEhE,UAAI,KAAK,OAAO,aAAa,SAAS,GAAG;AACxC,aAAK,OAAO,aAAa,GAAG,EAAE,EAAG,UAAU;MAC5C,OAAO;AACN,aAAK,OAAO,UAAU;MACvB;IACD,OAAO;AACN,YAAM,eAAe;AAErB,UAAI,KAAK,OAAO,aAAa,SAAS,GAAG;AACxC,aAAK,OAAO,aAAa,GAAG,EAAE,EAAG,UAAU;MAC5C,OAAO;AACN,aAAK,OAAO,UAAU;MACvB;IACD;AACA,WAAO;EACR;;;;;;;;;;;;;;;;;EAkBA,MAAM,OAAuE;AAC5E,QAAI,KAAK,OAAO,aAAa,SAAS,GAAG;AACxC,WAAK,OAAO,aAAa,GAAG,EAAE,EAAG,QAAQ;IAC1C,OAAO;AACN,WAAK,OAAO,QAAQ;IACrB;AACA,WAAO;EACR;;;;;;;;;;;;;;;;;EAkBA,OAAO,QAAyE;AAC/E,QAAI,KAAK,OAAO,aAAa,SAAS,GAAG;AACxC,WAAK,OAAO,aAAa,GAAG,EAAE,EAAG,SAAS;IAC3C,OAAO;AACN,WAAK,OAAO,SAAS;IACtB;AACA,WAAO;EACR;;;;;;;;;;;EAYA,IAAI,UAAwB,SAAqB,CAAC,GAA2C;AAC5F,SAAK,OAAO,gBAAgB,EAAE,UAAU,OAAO;AAC/C,WAAO;EACR;;EAGA,SAAc;AACb,WAAO,KAAK,QAAQ,iBAAiB,KAAK,MAAM;EACjD;EAEA,QAAe;AACd,UAAM,EAAE,SAAS,UAAU,GAAG,KAAK,IAAI,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC;AAC5E,WAAO;EACR;EAEA,GACCE,QAC6D;AAC7D,WAAO,IAAI;MACV,IAAI,SAAS,KAAK,OAAO,GAAG,KAAK,OAAO,QAAQA,MAAK;MACrD,IAAI,sBAAsB,EAAE,OAAAA,QAAO,oBAAoB,SAAS,aAAa,QAAQ,CAAC;IACvF;EACD;;EAGS,oBAAiD;AACzD,WAAO,IAAI;MACV,KAAK,OAAO;MACZ,IAAI,sBAAsB,EAAE,OAAO,KAAK,WAAW,oBAAoB,SAAS,aAAa,QAAQ,CAAC;IACvG;EACD;EAEA,WAAkC;AACjC,WAAO;EACR;AACD;AAvvBC,cAZqB,0BAYKF,MAAsB;AAjKjD,IAAAA,MAAAC;AAo7BO,IAAM,eAAN,eAUGA,MAAA,0BAWiBD,OAAA,YAXjBC,KAU4C;EApB/C;;AAmDE;AAOR,mCAAkD,CAAC,sBAAsB;AACxE,aAAO,OAAO,gBAAgB,qBAAqB,MAAM;AACxD,eAAO,KAAK,SAAS,EAAE,QAAQ,mBAAmB,KAAK,SAAS;MACjE,CAAC;IACF;;;EAtCA,SAAS,MAAsC;AAC9C,UAAM,EAAE,SAAS,QAAQ,SAAS,qBAAqB,UAAU,IAAI;AACrE,QAAI,CAAC,SAAS;AACb,YAAM,IAAI,MAAM,oFAAoF;IACrG;AACA,WAAO,OAAO,gBAAgB,wBAAwB,MAAM;AAC3D,YAAM,aAAa,oBAA8B,OAAO,MAAM;AAC9D,YAAM,QAAQ,QAAQ,aAEpB,QAAQ,WAAW,KAAK,OAAO,CAAC,GAAG,YAAY,MAAM,IAAI;AAC3D,YAAM,sBAAsB;AAE5B,aAAO,MAAM,SAAS,SAAS;IAChC,CAAC;EACF;;;;;;;;EASA,QAAQ,MAAqC;AAC5C,WAAO,KAAK,SAAS,IAAI;EAC1B;;EAIA,SAAS,OAAuB;AAC/B,SAAK,YAAY;AACjB,WAAO;EACR;AAOD;AA1CC,cArBY,cAqBcD,MAAsB;AA4CjD,YAAY,cAAc,CAAC,YAAY,CAAC;AAExC,SAAS,kBAAkB,MAAmB,OAAuC;AACpF,SAAO,CAAC,YAAY,gBAAgB,gBAAgB;AACnD,UAAM,eAAe,CAAC,aAAa,GAAG,WAAW,EAAE,IAAI,CAAC,YAAY;MACnE;MACA;MACA,aAAa;IACd,EAAE;AAEF,eAAW,eAAe,cAAc;AACvC,UAAI,CAAC,aAAc,WAAmB,kBAAkB,GAAG,YAAY,YAAY,kBAAkB,CAAC,GAAG;AACxG,cAAM,IAAI;UACT;QACD;MACD;IACD;AAEA,WAAQ,WAA2B,gBAAgB,YAAY;EAChE;AACD;AAEA,IAAM,oBAAoB,OAAO;EAChC;EACA;EACA;EACA;EACA;EACA;AACD;AA2BO,IAAM,QAAQ,kBAAkB,SAAS,KAAK;AA2B9C,IAAM,WAAW,kBAAkB,SAAS,IAAI;AA2BhD,IAAM,YAAY,kBAAkB,aAAa,KAAK;AA0CtD,IAAM,eAAe,kBAAkB,aAAa,IAAI;AA2BxD,IAAM,SAAS,kBAAkB,UAAU,KAAK;AA0ChD,IAAM,YAAY,kBAAkB,UAAU,IAAI;;;ACltCzD,IAAAG;AAakBA,OAAA;AADX,IAAM,eAAN,MAAmB;EAMzB,YAAY,SAAuC;AAH3C;AACA;AAOR,iCAAqB,CAACC,QAAe,cAAiC;AACrE,YAAM,eAAe;AACrB,YAAM,KAAK,CACV,OAII;AACJ,YAAI,OAAO,OAAO,YAAY;AAC7B,eAAK,GAAG,YAAY;QACrB;AAEA,eAAO,IAAI;UACV,IAAI;YACH,GAAG,OAAO;YACV,cAAc,uBAAuB,KAAK,GAAG,kBAAkB,KAAK,CAAC,IAAI,CAAC;YAC1EA;YACA;UACD;UACA,IAAI,sBAAsB,EAAE,OAAAA,QAAO,oBAAoB,SAAS,aAAa,QAAQ,CAAC;QACvF;MACD;AACA,aAAO,EAAE,GAAG;IACb;AA3BC,SAAK,UAAU,GAAG,SAAS,SAAS,IAAI,UAAU;AAClD,SAAK,gBAAgB,GAAG,SAAS,SAAS,IAAI,SAAY;EAC3D;EA2BA,QAAQ,SAAyB;AAChC,UAAM,OAAO;AAIb,aAAS,OACR,QACgD;AAChD,aAAO,IAAI,gBAAgB;QAC1B,QAAQ,UAAU;QAClB,SAAS;QACT,SAAS,KAAK,WAAW;QACzB,UAAU;MACX,CAAC;IACF;AAIA,aAAS,eACR,QACgD;AAChD,aAAO,IAAI,gBAAgB;QAC1B,QAAQ,UAAU;QAClB,SAAS;QACT,SAAS,KAAK,WAAW;QACzB,UAAU;MACX,CAAC;IACF;AAOA,aAAS,iBACR,IACA,QACgD;AAChD,aAAO,IAAI,gBAAgB;QAC1B,QAAQ,UAAU;QAClB,SAAS;QACT,SAAS,KAAK,WAAW;QACzB,UAAU,EAAE,GAAG;MAChB,CAAC;IACF;AAEA,WAAO,EAAE,QAAQ,gBAAgB,iBAAiB;EACnD;EAIA,OAA0C,QAAoE;AAC7G,WAAO,IAAI,gBAAgB;MAC1B,QAAQ,UAAU;MAClB,SAAS;MACT,SAAS,KAAK,WAAW;IAC1B,CAAC;EACF;EAIA,eAAkD,QAA8D;AAC/G,WAAO,IAAI,gBAAgB;MAC1B,QAAQ,UAAU;MAClB,SAAS;MACT,SAAS,KAAK,WAAW;MACzB,UAAU;IACX,CAAC;EACF;EAOA,iBACC,IACA,QAC0C;AAC1C,WAAO,IAAI,gBAAgB;MAC1B,QAAQ,UAAU;MAClB,SAAS;MACT,SAAS,KAAK,WAAW;MACzB,UAAU,EAAE,GAAG;IAChB,CAAC;EACF;;EAGQ,aAAa;AACpB,QAAI,CAAC,KAAK,SAAS;AAClB,WAAK,UAAU,IAAI,UAAU,KAAK,aAAa;IAChD;AAEA,WAAO,KAAK;EACb;AACD;AAlIC,cADY,cACKD,MAAsB;;;ACbxC,IAAAE;AAyDkBA,OAAA;AALX,IAAM,kBAAN,MAIL;EAGD,YACS,OACA,SACA,SACA,UACA,wBACP;AAEM;AAPC,SAAA,QAAA;AACA,SAAA,UAAA;AACA,SAAA,UAAA;AACA,SAAA,WAAA;AACA,SAAA,yBAAA;EACN;;EAIH,SAAS,OAAuB;AAC/B,SAAK,YAAY;AACjB,WAAO;EACR;EAEA,wBAAoG;AACnG,SAAK,yBAAyB;AAC9B,WAAO;EACR;EAIA,OACC,QACqC;AACrC,aAAS,MAAM,QAAQ,MAAM,IAAI,SAAS,CAAC,MAAM;AACjD,QAAI,OAAO,WAAW,GAAG;AACxB,YAAM,IAAI,MAAM,iDAAiD;IAClE;AACA,UAAM,eAAe,OAAO,IAAI,CAAC,UAAU;AAC1C,YAAM,SAAsC,CAAC;AAC7C,YAAM,OAAO,KAAK,MAAM,MAAM,OAAO,OAAO;AAC5C,iBAAW,UAAU,OAAO,KAAK,KAAK,GAAG;AACxC,cAAM,WAAW,MAAM,MAA4B;AACnD,eAAO,MAAM,IAAI,GAAG,UAAU,GAAG,IAAI,WAAW,IAAI,MAAM,UAAU,KAAK,MAAM,CAAC;MACjF;AACA,aAAO;IACR,CAAC;AAED,WAAO,IAAI;MACV,KAAK;MACL;MACA,KAAK;MACL,KAAK;MACL,KAAK;MACL;MACA,KAAK;IACN,EAAE,SAAS,KAAK,SAAS;EAC1B;EAMA,OACC,aAIqC;AACrC,UAAM,SAAS,OAAO,gBAAgB,aAAa,YAAY,IAAI,aAAa,CAAC,IAAI;AAErF,QACC,CAAC,GAAG,QAAQ,GAAG,KACZ,CAAC,aAAa,KAAK,MAAM,OAAO,GAAG,OAAO,EAAE,cAAc,GAC5D;AACD,YAAM,IAAI;QACT;MACD;IACD;AAEA,WAAO,IAAI,aAAa,KAAK,OAAO,QAAQ,KAAK,SAAS,KAAK,SAAS,KAAK,UAAU,IAAI;EAC5F;AACD;AA3EC,cALY,iBAKKA,MAAsB;AAzDxC,IAAAA,MAAAC;AAsOO,IAAM,eAAN,eASGA,MAAA,cASiBD,OAAA,YATjBC,KAQV;EAKC,YACC,OACA,QACQ,SACA,SACR,UACA,QACA,wBACC;AACD,UAAM;AAXC;AAkKA;AAOC,mCAAkD,CAAC,sBAAsB;AACjF,aAAO,OAAO,gBAAgB,qBAAqB,MAAM;AACxD,eAAO,KAAK,SAAS,EAAE,QAAQ,mBAAmB,KAAK,SAAS;MACjE,CAAC;IACF;AAxKS,SAAA,UAAA;AACA,SAAA,UAAA;AAMR,SAAK,SAAS,EAAE,OAAO,QAAuB,UAAU,QAAQ,uBAAuB;EACxF;EA0BA,UACC,SAA6B,KAAK,OAAO,MAAM,MAAM,OAAO,OAAO,GACb;AACtD,SAAK,OAAO,kBAAkB;AAC9B,SAAK,OAAO,YAAY,oBAA8B,MAAM;AAC5D,WAAO;EACR;;;;;;;;;;;;;;;;;;;;;;;EAwBA,oBACC,SAAgE,CAAC,GACe;AAChF,QAAI,OAAO,WAAW,QAAW;AAChC,WAAK,OAAO,aAAa;IAC1B,OAAO;AACN,UAAI,eAAe;AACnB,qBAAe,MAAM,QAAQ,OAAO,MAAM,IACvC,OAAO,OAAO,IAAI,CAAC,OAAO,KAAK,QAAQ,WAAW,KAAK,QAAQ,OAAO,gBAAgB,EAAE,CAAC,CAAC,EAAE,KAAK,GAAG,IACpG,KAAK,QAAQ,WAAW,KAAK,QAAQ,OAAO,gBAAgB,OAAO,MAAM,CAAC;AAE7E,YAAM,WAAW,OAAO,QAAQ,aAAa,OAAO,KAAK,KAAK;AAC9D,WAAK,OAAO,aAAa,OAAO,IAAI,IAAI,YAAY,CAAC,IAAI,QAAQ;IAClE;AACA,WAAO;EACR;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA+BA,mBACC,QACgF;AAChF,QAAI,OAAO,UAAU,OAAO,eAAe,OAAO,WAAW;AAC5D,YAAM,IAAI;QACT;MACD;IACD;AACA,UAAM,WAAW,OAAO,QAAQ,aAAa,OAAO,KAAK,KAAK;AAC9D,UAAM,iBAAiB,OAAO,cAAc,aAAa,OAAO,WAAW,KAAK;AAChF,UAAM,cAAc,OAAO,WAAW,aAAa,OAAO,QAAQ,KAAK;AACvE,UAAM,SAAS,KAAK,QAAQ,eAAe,KAAK,OAAO,OAAO,aAAa,KAAK,OAAO,OAAO,OAAO,GAAG,CAAC;AACzG,QAAI,eAAe;AACnB,mBAAe,MAAM,QAAQ,OAAO,MAAM,IACvC,OAAO,OAAO,IAAI,CAAC,OAAO,KAAK,QAAQ,WAAW,KAAK,QAAQ,OAAO,gBAAgB,EAAE,CAAC,CAAC,EAAE,KAAK,GAAG,IACpG,KAAK,QAAQ,WAAW,KAAK,QAAQ,OAAO,gBAAgB,OAAO,MAAM,CAAC;AAC7E,SAAK,OAAO,aAAa,OACxB,IAAI,IAAI,YAAY,CACrB,IAAI,cAAc,kBAAkB,MAAM,GAAG,QAAQ,GAAG,WAAW;AACnE,WAAO;EACR;;EAGA,SAAc;AACb,WAAO,KAAK,QAAQ,iBAAiB,KAAK,MAAM;EACjD;EAEA,QAAe;AACd,UAAM,EAAE,SAAS,UAAU,GAAG,KAAK,IAAI,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC;AAC5E,WAAO;EACR;;EAGA,SAAS,MAAsC;AAC9C,WAAO,OAAO,gBAAgB,wBAAwB,MAAM;AAC3D,aAAO,KAAK,QAAQ,aAIlB,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC,GAAG,KAAK,OAAO,WAAW,MAAM,IAAI;IAC5E,CAAC;EACF;EAEA,QAAQ,MAAqC;AAC5C,WAAO,KAAK,SAAS,IAAI;EAC1B;;EAIA,SAAS,OAAuB;AAC/B,SAAK,YAAY;AACjB,WAAO;EACR;;EASA,oBAAiD;AAChD,WACC,KAAK,OAAO,kBACT,IAAI;MACL,KAAK,OAAO;MACZ,IAAI,sBAAsB;QACzB,OAAO,aAAa,KAAK,OAAO,KAAK;QACrC,oBAAoB;QACpB,aAAa;MACd,CAAC;IACF,IACE;EAEL;EAEA,WAAkC;AACjC,WAAO;EACR;AACD;AApMC,cAlBY,cAkBcD,MAAsB;;;ACxPjD,IAAAE,MAAAC;AA6BO,IAAM,4BAAN,eACEA,MAAA,cAGkBD,OAAA,YAHlBC,KAET;EASC,YACC,MACQ,SACA,SACP;AACD,UAAM;AAXC;AA4DA;AAOR,mCAAkD,CAAC,sBAAsB;AACxE,aAAO,OAAO,gBAAgB,qBAAqB,MAAM;AACxD,eAAO,KAAK,SAAS,EAAE,QAAQ,mBAAmB,KAAK,SAAS;MACjE,CAAC;IACF;AA/DS,SAAA,UAAA;AACA,SAAA,UAAA;AAGR,SAAK,SAAS,EAAE,KAAK;EACtB;EAEA,eAAqB;AACpB,QAAI,KAAK,OAAO,eAAe,QAAW;AACzC,YAAM,IAAI,MAAM,iDAAiD;IAClE;AACA,SAAK,OAAO,eAAe;AAC3B,WAAO;EACR;EAEA,aAAmB;AAClB,QAAI,KAAK,OAAO,iBAAiB,QAAW;AAC3C,YAAM,IAAI,MAAM,iDAAiD;IAClE;AACA,SAAK,OAAO,aAAa;AACzB,WAAO;EACR;;EAGA,SAAc;AACb,WAAO,KAAK,QAAQ,kCAAkC,KAAK,MAAM;EAClE;EAEA,QAAe;AACd,UAAM,EAAE,SAAS,UAAU,GAAG,KAAK,IAAI,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC;AAC5E,WAAO;EACR;;EAGA,SAAS,MAIP;AACD,WAAO,OAAO,gBAAgB,wBAAwB,MAAM;AAC3D,aAAO,KAAK,QAAQ,aAAa,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC,GAAG,QAAW,MAAM,IAAI;IAC/F,CAAC;EACF;EAEA,QAAQ,MAIN;AACD,WAAO,KAAK,SAAS,IAAI;EAC1B;;EAIA,SAAS,OAAsB;AAC9B,SAAK,YAAY;AACjB,WAAO;EACR;AAOD;AA1EC,cAJY,2BAIcD,MAAsB;;;AChCjD,IAAAE;AAsEkBA,OAAA;AADX,IAAM,kBAAN,MAAqF;EAO3F,YACS,OACA,SACA,SACA,UACP;AAEM;AANC,SAAA,QAAA;AACA,SAAA,UAAA;AACA,SAAA,UAAA;AACA,SAAA,WAAA;EACN;EAGH,SAAS,OAAsB;AAC9B,SAAK,YAAY;AACjB,WAAO;EACR;EAEA,IACC,QACkH;AAClH,WAAO,IAAI;MACV,KAAK;MACL,aAAa,KAAK,OAAO,MAAM;MAC/B,KAAK;MACL,KAAK;MACL,KAAK;IACN,EAAE,SAAS,KAAK,SAAS;EAC1B;AACD;AA9BC,cADY,iBACKA,MAAsB;AAtExC,IAAAA,MAAAC;AAiVO,IAAM,eAAN,eAcGA,MAAA,cAKiBD,OAAA,YALjBC,KAIV;EAOC,YACC,OACA,KACQ,SACA,SACR,UACC;AACD,UAAM;AAXC;AACA;AACA;AAqGR,oCAAW,KAAK,WAAW,MAAM;AAEjC,qCAAY,KAAK,WAAW,OAAO;AAEnC,qCAAY,KAAK,WAAW,OAAO;AAEnC,oCAAW,KAAK,WAAW,MAAM;AAuHzB;AAOC,mCAAkD,CAAC,sBAAsB;AACjF,aAAO,KAAK,SAAS,EAAE,QAAQ,mBAAmB,KAAK,SAAS;IACjE;AAtOS,SAAA,UAAA;AACA,SAAA,UAAA;AAIR,SAAK,SAAS,EAAE,KAAK,OAAO,UAAU,OAAO,CAAC,EAAE;AAChD,SAAK,YAAY,iBAAiB,KAAK;AACvC,SAAK,sBAAsB,OAAO,KAAK,cAAc,WAAW,EAAE,CAAC,KAAK,SAAS,GAAG,KAAK,IAAI,CAAC;EAC/F;EAEA,KACC,QAI2C;AAC3C,UAAM,MAAM;AACZ,UAAM,YAAY,iBAAiB,GAAG;AACtC,QAAI,OAAO,cAAc,UAAU;AAClC,WAAK,oBAAoB,SAAS,IAAI;IACvC;AACA,SAAK,OAAO,OAAO;AACnB,WAAO;EACR;EAEQ,mBAAmB,OAAiE;AAC3F,QAAI,GAAG,OAAO,OAAO,GAAG;AACvB,aAAO,MAAM,MAAM,OAAO,OAAO;IAClC,WAAW,GAAG,OAAO,QAAQ,GAAG;AAC/B,aAAO,MAAM,EAAE;IAChB;AACA,WAAO,MAAM,cAAc,EAAE;EAC9B;EAEQ,WACP,UAC4C;AAC5C,WAAQ,CACP,OACA,OACI;AACJ,YAAM,YAAY,iBAAiB,KAAK;AAExC,UAAI,OAAO,cAAc,YAAY,KAAK,OAAO,MAAM,KAAK,CAAC,SAAS,KAAK,UAAU,SAAS,GAAG;AAChG,cAAM,IAAI,MAAM,UAAU,SAAS,iCAAiC;MACrE;AAEA,UAAI,OAAO,OAAO,YAAY;AAC7B,cAAM,OAAO,KAAK,OAAO,QAAQ,CAAC,GAAG,KAAK,OAAO,MAAM,GAAG,IACvD,KAAK,mBAAmB,KAAK,OAAO,IAAI,IACxC;AACH,aAAK;UACJ,IAAI;YACH,KAAK,OAAO,MAAM,MAAM,OAAO,OAAO;YACtC,IAAI,sBAAsB,EAAE,oBAAoB,OAAO,aAAa,MAAM,CAAC;UAC5E;UACA,QAAQ,IAAI;YACX;YACA,IAAI,sBAAsB,EAAE,oBAAoB,OAAO,aAAa,MAAM,CAAC;UAC5E;QACD;MACD;AAEA,WAAK,OAAO,MAAM,KAAK,EAAE,IAAI,OAAO,UAAU,OAAO,UAAU,CAAC;AAEhE,UAAI,OAAO,cAAc,UAAU;AAClC,gBAAQ,UAAU;UACjB,KAAK,QAAQ;AACZ,iBAAK,oBAAoB,SAAS,IAAI;AACtC;UACD;UACA,KAAK,SAAS;AACb,iBAAK,sBAAsB,OAAO;cACjC,OAAO,QAAQ,KAAK,mBAAmB,EAAE,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,KAAK,CAAC;YACrE;AACA,iBAAK,oBAAoB,SAAS,IAAI;AACtC;UACD;UACA,KAAK,SAAS;AACb,iBAAK,oBAAoB,SAAS,IAAI;AACtC;UACD;UACA,KAAK,QAAQ;AACZ,iBAAK,sBAAsB,OAAO;cACjC,OAAO,QAAQ,KAAK,mBAAmB,EAAE,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,KAAK,KAAK,CAAC;YACrE;AACA,iBAAK,oBAAoB,SAAS,IAAI;AACtC;UACD;QACD;MACD;AAEA,aAAO;IACR;EACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA2CA,MAAM,OAAkE;AACvE,SAAK,OAAO,QAAQ;AACpB,WAAO;EACR;EA4BA,UACC,QACsD;AACtD,QAAI,CAAC,QAAQ;AACZ,eAAS,OAAO,OAAO,CAAC,GAAG,KAAK,OAAO,MAAM,MAAM,OAAO,OAAO,CAAC;AAElE,UAAI,KAAK,OAAO,MAAM;AACrB,cAAM,YAAY,iBAAiB,KAAK,OAAO,IAAI;AAEnD,YAAI,OAAO,cAAc,YAAY,KAAK,OAAO,QAAQ,CAAC,GAAG,KAAK,OAAO,MAAM,GAAG,GAAG;AACpF,gBAAM,aAAa,KAAK,mBAAmB,KAAK,OAAO,IAAI;AAC3D,iBAAO,SAAS,IAAI;QACrB;AAEA,mBAAW,QAAQ,KAAK,OAAO,OAAO;AACrC,gBAAMC,aAAY,iBAAiB,KAAK,KAAK;AAE7C,cAAI,OAAOA,eAAc,YAAY,CAAC,GAAG,KAAK,OAAO,GAAG,GAAG;AAC1D,kBAAM,aAAa,KAAK,mBAAmB,KAAK,KAAK;AACrD,mBAAOA,UAAS,IAAI;UACrB;QACD;MACD;IACD;AAEA,SAAK,OAAO,kBAAkB;AAC9B,SAAK,OAAO,YAAY,oBAA8B,MAAM;AAC5D,WAAO;EACR;;EAGA,SAAc;AACb,WAAO,KAAK,QAAQ,iBAAiB,KAAK,MAAM;EACjD;EAEA,QAAe;AACd,UAAM,EAAE,SAAS,UAAU,GAAG,KAAK,IAAI,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC;AAC5E,WAAO;EACR;;EAGA,SAAS,MAAsC;AAC9C,UAAM,QAAQ,KAAK,QAAQ,aAEzB,KAAK,QAAQ,WAAW,KAAK,OAAO,CAAC,GAAG,KAAK,OAAO,WAAW,MAAM,IAAI;AAC3E,UAAM,sBAAsB,KAAK;AACjC,WAAO;EACR;EAEA,QAAQ,MAAqC;AAC5C,WAAO,KAAK,SAAS,IAAI;EAC1B;;EAIA,SAAS,OAAuB;AAC/B,SAAK,YAAY;AACjB,WAAO;EACR;;EAOA,oBAAiD;AAChD,WACC,KAAK,OAAO,kBACT,IAAI;MACL,KAAK,OAAO;MACZ,IAAI,sBAAsB;QACzB,OAAO,aAAa,KAAK,OAAO,KAAK;QACrC,oBAAoB;QACpB,aAAa;MACd,CAAC;IACF,IACE;EAEL;EAEA,WAAkC;AACjC,WAAO;EACR;AACD;AApQC,cAnBY,cAmBcF,MAAsB;;;ACrWjD,IAAAG,MAAAC,KAAA;AAMO,IAAM,kBAAN,MAAM,yBAEH,UAIiBA,MAAA,YACzBD,OAAA,OAAO,aALC,IAAmD;EAuB5D,YACU,QAKR;AACD,UAAM,gBAAe,mBAAmB,OAAO,QAAQ,OAAO,OAAO,EAAE,WAAW;AA7B3E;AACA;AAGR,wBAACA,MAAsB;AAEf;AAiBE,SAAA,SAAA;AAQT,SAAK,QAAQ,MAAM;AAEnB,SAAK,UAAU,OAAO;AAEtB,SAAK,MAAM,gBAAe;MACzB,OAAO;MACP,OAAO;IACR;EACD;EA/BA,OAAe,mBACd,QACA,SACc;AACd,WAAO,4BAAoC,MAAM,GAAG,IAAI,IAAI,SAAS,EAAE,GAAG,OAAO,CAAC,GAAG,OAAO;EAC7F;EAEA,OAAe,WACd,QACA,SACc;AACd,WAAO,oCAA4C,MAAM,GAAG,IAAI,IAAI,SAAS,EAAE,GAAG,OAAO,CAAC,GAAG,OAAO;EACrG;;EAsBA,SAAS,OAAuB;AAC/B,SAAK,QAAQ;AACb,WAAO;EACR;EAEA,KACC,aACA,YAC+B;AAC/B,WAAO,QAAQ,QAAQ,KAAK,QAAQ,MAAM,KAAK,KAAK,KAAK,KAAK,CAAC,EAC7D;MACA;MACA;IACD;EACF;EAEA,MACC,YACkB;AAClB,WAAO,KAAK,KAAK,QAAW,UAAU;EACvC;EAEA,QAAQ,WAA8D;AACrE,WAAO,KAAK;MACX,CAAC,UAAU;AACV;AACA,eAAO;MACR;MACA,CAAC,WAAW;AACX;AACA,cAAM;MACP;IACD;EACD;AACD;AAzEC,cANY,iBAMcC,KAAc;AANlC,IAAM,iBAAN;;;ACNP,IAAAC;AAmBkBA,OAAA;AADX,IAAM,yBAAN,MAA4G;EAGlH,YACS,YACA,QACA,eACA,OACA,aACA,SACA,SACP;AAPO,SAAA,aAAA;AACA,SAAA,SAAA;AACA,SAAA,gBAAA;AACA,SAAA,QAAA;AACA,SAAA,cAAA;AACA,SAAA,UAAA;AACA,SAAA,UAAA;EACN;EAEH,SACC,QACmE;AACnE,WAAO,IAAI;MACV,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,SAAU,SAAyC,CAAC;MACpD;IACD;EACD;EAEA,UACC,QACgF;AAChF,WAAO,IAAI;MACV,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,KAAK;MACL,SAAS,EAAE,GAAI,QAAoD,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE;MAC3F;IACD;EACD;AACD;AA3CC,cADY,wBACKA,MAAsB;AAnBxC,IAAAA,MAAAC;AAgEO,IAAM,oBAAN,eAAyCA,MAAA,cAGrBD,OAAA,YAHqBC,KAEhD;EAQC,YACS,YACA,QACA,eACA,OACA,aACA,SACA,SACA,QACA,MACP;AACD,UAAM;AA2DC;AArEC,SAAA,aAAA;AACA,SAAA,SAAA;AACA,SAAA,gBAAA;AACA,SAAA,QAAA;AACA,SAAA,cAAA;AACA,SAAA,UAAA;AACA,SAAA,UAAA;AACA,SAAA,SAAA;AACA,SAAA,OAAA;EAGT;;EAGA,SAAS,MAA4E;AACpF,WAAO,OAAO,gBAAgB,wBAAwB,MAAM;AAC3D,YAAM,EAAE,OAAO,WAAW,IAAI,KAAK,OAAO;AAE1C,aAAO,KAAK,QAAQ;QACnB;QACA;QACA;QACA;QACA,CAAC,SAAS,mBAAmB;AAC5B,gBAAM,OAAO,QAAQ;YAAI,CAAC,QACzB,iBAAiB,KAAK,QAAQ,KAAK,aAAa,KAAK,MAAM,WAAW,cAAc;UACrF;AACA,cAAI,KAAK,SAAS,SAAS;AAC1B,mBAAO,KAAK,CAAC;UACd;AACA,iBAAO;QACR;MACD;IACD,CAAC;EACF;EAEA,QAAQ,MAA2E;AAClF,WAAO,KAAK,SAAS,IAAI;EAC1B;EAEQ,YAAY;AACnB,WAAO,KAAK,QAAQ,8BAA8B;MACjD,YAAY,KAAK;MACjB,QAAQ,KAAK;MACb,eAAe,KAAK;MACpB,OAAO,KAAK;MACZ,aAAa,KAAK;MAClB,aAAa,KAAK;MAClB,YAAY,KAAK,YAAY;IAC9B,CAAC;EACF;;EAGA,SAAc;AACb,WAAO,KAAK,UAAU,EAAE;EACzB;EAEQ,SAA8E;AACrF,UAAM,QAAQ,KAAK,UAAU;AAE7B,UAAM,aAAa,KAAK,QAAQ,WAAW,MAAM,GAAU;AAE3D,WAAO,EAAE,OAAO,WAAW;EAC5B;EAEA,QAAe;AACd,WAAO,KAAK,OAAO,EAAE;EACtB;;EAIA,SAAS,OAAuB;AAC/B,SAAK,YAAY;AACjB,WAAO;EACR;EAES,UAA4B;AACpC,WAAO,OAAO,gBAAgB,qBAAqB,MAAM;AACxD,aAAO,KAAK,SAAS,EAAE,QAAQ,QAAW,KAAK,SAAS;IACzD,CAAC;EACF;AACD;AAzFC,cAHY,mBAGcD,MAAsB;;;ACnEjD,IAAAE,MAAAC;AAQO,IAAM,QAAN,eAA6BA,OAAA,cAGTD,OAAA,YAHSC,MAEpC;EAQC,YACQ,SACCC,MACA,OACA,gBACP;AACD,UAAM;AALC,SAAA,UAAA;AACC,SAAA,MAAAA;AACA,SAAA,QAAA;AACA,SAAA,iBAAA;EAGT;;EAGA,SAAS;AACR,WAAO,KAAK;EACb;EAEA,WAAW;AACV,WAAO,KAAK;EACb;EAEA,UAAU,QAAiB,aAAuB;AACjD,WAAO,cAAc,KAAK,eAAe,MAAM,IAAI;EACpD;EAEA,WAA0B;AACzB,WAAO;EACR;;EAGA,wBAAwB;AACvB,WAAO;EACR;AACD;AArCC,cAHY,OAGcF,MAAsB;;;ACXjD,IAAAG;AAuCkBA,OAAA;AALX,IAAM,aAAN,MAIL;EAgBD,YAEU,SAEA,SACT,QACC;AAZF;AA0EA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;iCAAqB,CAACC,QAAe,cAAiC;AACrE,YAAM,OAAO;AACb,YAAM,KAAK,CACV,OAII;AACJ,YAAI,OAAO,OAAO,YAAY;AAC7B,eAAK,GAAG,IAAI,aAAa,KAAK,OAAO,CAAC;QACvC;AAEA,eAAO,IAAI;UACV,IAAI;YACH,GAAG,OAAO;YACV,cAAc,uBAAuB,KAAK,GAAG,kBAAkB,KAAK,CAAC,IAAI,CAAC;YAC1EA;YACA;UACD;UACA,IAAI,sBAAsB,EAAE,OAAAA,QAAO,oBAAoB,SAAS,aAAa,QAAQ,CAAC;QACvF;MACD;AACA,aAAO,EAAE,GAAG;IACb;AA+cU;AAxiBA,SAAA,UAAA;AAEA,SAAA,UAAA;AAGT,SAAK,IAAI,SACN;MACD,QAAQ,OAAO;MACf,YAAY,OAAO;MACnB,eAAe,OAAO;MACtB;IACD,IACE;MACD,QAAQ;MACR,YAAY,CAAC;MACb,eAAe,CAAC;MAChB;IACD;AACD,SAAK,QAAQ,CAAC;AACd,QAAI,KAAK,EAAE,QAAQ;AAClB,iBAAW,CAAC,WAAW,OAAO,KAAK,OAAO,QAAQ,KAAK,EAAE,MAAM,GAAG;AAChE,aAAK,MAAiE,SAAS,IAAI,IAAI;UACvF,OAAQ;UACR,KAAK,EAAE;UACP,KAAK,EAAE;UACP,OAAQ,WAAW,SAAS;UAC5B;UACA;UACA;QACD;MACD;IACD;EACD;EA2DA,OACC,QACA,SACC;AACD,WAAO,IAAI,eAAe,EAAE,QAAQ,SAAS,SAAS,KAAK,QAAQ,CAAC;EACrE;;;;;;;;;;;;;;;;;;;;EAqBA,QAAQ,SAAyB;AAChC,UAAM,OAAO;AAwCb,aAAS,OAA0C,QAA8D;AAChH,aAAO,IAAI,gBAAgB;QAC1B,QAAQ,UAAU;QAClB,SAAS,KAAK;QACd,SAAS,KAAK;QACd,UAAU;MACX,CAAC;IACF;AA4BA,aAAS,eACR,QAC0C;AAC1C,aAAO,IAAI,gBAAgB;QAC1B,QAAQ,UAAU;QAClB,SAAS,KAAK;QACd,SAAS,KAAK;QACd,UAAU;QACV,UAAU;MACX,CAAC;IACF;AAgCA,aAAS,iBACR,IACA,QAC0C;AAC1C,aAAO,IAAI,gBAAgB;QAC1B,QAAQ,UAAU;QAClB,SAAS,KAAK;QACd,SAAS,KAAK;QACd,UAAU;QACV,UAAU,EAAE,GAAG;MAChB,CAAC;IACF;AA6BA,aAAS,OAA+B,OAAsD;AAC7F,aAAO,IAAI,gBAAgB,OAAO,KAAK,SAAS,KAAK,SAAS,OAAO;IACtE;AA0BA,aAAS,OAA+B,OAAsD;AAC7F,aAAO,IAAI,gBAAgB,OAAO,KAAK,SAAS,KAAK,SAAS,OAAO;IACtE;AA0BA,aAAS,QAAgC,OAAmD;AAC3F,aAAO,IAAI,aAAa,OAAO,KAAK,SAAS,KAAK,SAAS,OAAO;IACnE;AAEA,WAAO,EAAE,QAAQ,gBAAgB,kBAAkB,QAAQ,QAAQ,QAAQ,QAAQ;EACpF;EAwCA,OAA0C,QAA8D;AACvG,WAAO,IAAI,gBAAgB;MAC1B,QAAQ,UAAU;MAClB,SAAS,KAAK;MACd,SAAS,KAAK;IACf,CAAC;EACF;EA4BA,eAAkD,QAA8D;AAC/G,WAAO,IAAI,gBAAgB;MAC1B,QAAQ,UAAU;MAClB,SAAS,KAAK;MACd,SAAS,KAAK;MACd,UAAU;IACX,CAAC;EACF;EAgCA,iBACC,IACA,QAC0C;AAC1C,WAAO,IAAI,gBAAgB;MAC1B,QAAQ,UAAU;MAClB,SAAS,KAAK;MACd,SAAS,KAAK;MACd,UAAU,EAAE,GAAG;IAChB,CAAC;EACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA6BA,OAA+B,OAAsD;AACpF,WAAO,IAAI,gBAAgB,OAAO,KAAK,SAAS,KAAK,OAAO;EAC7D;;;;;;;;;;;;;;;;;;;;;;;;;EA0BA,OAA+B,OAAsD;AACpF,WAAO,IAAI,gBAAgB,OAAO,KAAK,SAAS,KAAK,OAAO;EAC7D;;;;;;;;;;;;;;;;;;;;;;;;;EA0BA,OAA+B,OAAmD;AACjF,WAAO,IAAI,aAAa,OAAO,KAAK,SAAS,KAAK,OAAO;EAC1D;EAEA,wBAA0D,MAAsD;AAC/G,WAAO,IAAI,0BAA0B,MAAM,KAAK,SAAS,KAAK,OAAO;EACtE;EAIA,QACC,OAC+C;AAC/C,UAAM,SAAS,OAAO,UAAU,WAAW,IAAI,IAAI,KAAK,IAAI,MAAM,OAAO;AACzE,UAAM,aAAa,KAAK,QAAQ,WAAW,MAAM;AACjD,UAAM,WAAW,KAAK,QAAQ;MAG7B;MACA;MACA;MACA;IACD;AACA,WAAO,IAAI;MACV,MAAM,SAAS,QAAQ,QAAW,KAAK,SAAS;MAChD;MACA;MACA,CAAC,WAAW,SAAS,UAAU,QAAQ,IAAI;IAC5C;EACD;EAEA,YACC,aACA,QACa;AACb,WAAO,KAAK,QAAQ,YAAY,aAAa,MAAM;EACpD;AACD;AAtlBC,cALY,YAKKD,MAAsB;AA0lBjC,IAAM,eAAe,CAU3B,SACA,UACA,aAAmC,MAAM,SAAS,KAAK,MAAM,KAAK,OAAO,IAAI,SAAS,MAAM,CAAC,MACtE;AACvB,QAAM,SAAsB,IAAI,SAAa,WAAW,QAAQ,EAAE,OAAO,GAAG,IAAI;AAChF,QAAM,iBAAsC,IAAI,SAAa,WAAW,QAAQ,EAAE,eAAe,GAAG,IAAI;AACxG,QAAM,mBAA0C,IAAI,SAAgB,WAAW,QAAQ,EAAE,iBAAiB,GAAG,IAAI;AACjH,QAAM,SAAsB,IAAI,SAAgB,WAAW,QAAQ,EAAE,OAAO,GAAG,IAAI;AACnF,QAAM,QAAmB,IAAI,SAAc,WAAW,QAAQ,EAAE,KAAK,GAAG,IAAI;AAC5E,QAAM,QAAoB,CAAC,QAAa,WAAW,QAAQ,EAAE,MAAM,GAAG;AAEtE,QAAM,SAAsB,IAAI,SAAgB,QAAQ,OAAO,GAAG,IAAI;AACtE,QAAM,SAAsB,IAAI,SAAgB,QAAQ,OAAO,GAAG,IAAI;AACtE,QAAM,UAAuB,IAAI,SAAgB,QAAQ,OAAO,GAAG,IAAI;AACvE,QAAM,UAAwB,IAAI,SAAgB,QAAQ,QAAQ,GAAG,IAAI;AACzE,QAAM,cAAgC,IAAI,SAAgB,QAAQ,YAAY,GAAG,IAAI;AACrF,QAAM,0BAAwD,IAAI,SACjE,QAAQ,wBAAwB,GAAG,IAAI;AAExC,SAAO;IACN,GAAG;IACH;IACA;IACA,QAAQ;IACR;IACA;IACA;IACA,UAAU;IACV;IACA;IACA;IACA;IACA;IACA,MAAM;IACN,IAAI,QAAQ;AACX,aAAO,WAAW,QAAQ,EAAE;IAC7B;EACD;AACD;;;ACjrBA,IAAAE;AA6GkBA,OAAA;AADX,IAAM,iBAAN,MAAqB;EAG3B,YAAoBC,SAAyB,MAAe;AAAxC,SAAA,SAAAA;AAAyB,SAAA,OAAA;EAAgB;EAE7D,MAAM,SAAkG;AACvG,WAAO,IAAI;MACV,QAAQ,IAAI,CAAC,OAAO;AACnB,YAAI,GAAG,IAAI,GAAG,GAAG;AAChB,iBAAO;QACR;AACA,aAAK;AACL,cAAM,sBAAsB,IAAI,cAAc,GAAG,MAAM,CAAC,CAAC,GAAG,WAAW,GAAG,YAAa,GAAG,WAAY;AACtG,WAAG,cAAc,KAAK,MAAM,KAAK,UAAU,GAAG,aAAa,CAAC;AAC5D,eAAO;MACR,CAAC;MACD,KAAK;MACL;MACA,KAAK;IACN;EACD;EAEA,UAAU,SAAkG;AAC3G,WAAO,IAAI;MACV,QAAQ,IAAI,CAAC,OAAO;AACnB,YAAI,GAAG,IAAI,GAAG,GAAG;AAChB,iBAAO;QACR;AACA,aAAK;AACL,cAAM,sBAAsB,IAAI,cAAc,GAAG,MAAM,CAAC,CAAC,GAAG,WAAW,GAAG,YAAa,GAAG,WAAY;AACtG,WAAG,cAAc,GAAG;AACpB,eAAO;MACR,CAAC;MACD,KAAK;MACL;MACA,KAAK;IACN;EACD;;;;;;;;;;;;EAaA,MACC,WACG,SACY;AACf,WAAO,IAAI;MACV,QAAQ,IAAI,CAAC,OAAO;AACnB,YAAI,GAAG,IAAI,GAAG,GAAG;AAChB,iBAAO;QACR;AACA,aAAK;AACL,cAAM,sBAAsB,IAAI,cAAc,GAAG,MAAM,CAAC,CAAC,GAAG,WAAW,GAAG,YAAa,GAAG,WAAY;AACtG,WAAG,cAAc,KAAK,MAAM,KAAK,UAAU,GAAG,aAAa,CAAC;AAC5D,eAAO;MACR,CAAC;MACD,KAAK;MACL;MACA,KAAK;MACL;IACD;EACD;AACD;AArEC,cADY,gBACKD,MAAsB;AA7GxC,IAAAA;AA4LkBA,OAAA;AADX,IAAM,eAAN,MAA8C;EAMpD,YACC,SACAC,SACA,MACA,MACA,SAAiB,SAChB;AARF;;AASC,SAAK,SAAS;MACb;MACA;MACA,QAAAA;MACA;MACA;IACD;EACD;EAEA,eAAqB;AACpB,SAAK,OAAO,eAAe;AAC3B,WAAO;EACR;EAEA,KAAK,KAAgC;AACpC,SAAK,OAAO,OAAO;AACnB,WAAO;EACR;EAEA,MAAM,WAAsB;AAC3B,SAAK,OAAO,QAAQ;AACpB,WAAO;EACR;;EAGA,MAAM,OAAuB;AAC5B,WAAO,IAAI,MAAM,KAAK,QAAQ,KAAK;EACpC;AACD;AAxCC,cADY,cACKD,MAAsB;AA5LxC,IAAAA;AAuOkBA,OAAA;AADX,IAAM,QAAN,MAAY;EAKlB,YAAY,QAAqB,OAAgB;AAFxC;AAGR,SAAK,SAAS,EAAE,GAAG,QAAQ,MAAM;EAClC;AACD;AAPC,cADY,OACKA,MAAsB;AAajC,SAAS,MAAM,MAA+B;AACpD,SAAO,IAAI,eAAe,OAAO,IAAI;AACtC;AAEO,SAAS,YAAY,MAA+B;AAC1D,SAAO,IAAI,eAAe,MAAM,IAAI;AACrC;;;AC1PA,IAAAE;AAuBkBA,OAAA;AADX,IAAM,WAAN,MAAyC;EAY/C,YACU,MACT,QACC;AAZO;AACA;AACA;AACA;AACA;AAGT;;AAGU,SAAA,OAAA;AAGT,QAAI,QAAQ;AACX,WAAK,KAAK,OAAO;AACjB,WAAK,MAAM,OAAO;AAClB,WAAK,KAAK,OAAO;AACjB,WAAK,QAAQ,OAAO;AACpB,WAAK,YAAY,OAAO;IACzB;EACD;EAEA,KAAK,OAAsB;AAC1B,SAAK,eAAe;AACpB,WAAO;EACR;AACD;AA5BC,cADY,UACKA,MAAsB;AA8BjC,SAAS,SAAS,MAAc,QAAyB;AAC/D,SAAO,IAAI,SAAS,MAAM,MAAM;AACjC;;;ACvDA,IAAAC;AASkBA,OAAA;AADX,IAAM,SAAN,MAAqC;EAa3C,YACU,MACT,QACC;AAZF;;AAGS;;AAEA;;AAEA;;AAGC,SAAA,OAAA;AAGT,QAAI,QAAQ;AACX,WAAK,WAAW,OAAO;AACvB,WAAK,aAAa,OAAO;AACzB,WAAK,UAAU,OAAO;IACvB;EACD;EAEA,WAAiB;AAChB,SAAK,YAAY;AACjB,WAAO;EACR;AACD;AA3BC,cADY,QACKA,MAAsB;AA6BjC,SAAS,OAAO,MAAc,QAAuB;AAC3D,SAAO,IAAI,OAAO,MAAM,MAAM;AAC/B;;;ACxCA,IAAAC;AAYkBA,OAAA;AADX,IAAM,aAAN,MAAiB;EAGvB,YACiB,SACA,YACA,QACf;AAHe,SAAA,UAAA;AACA,SAAA,aAAA;AACA,SAAA,SAAA;EAEjB;AACD;AARC,cADY,YACKA,MAAsB;AAUjC,SAAS,WACf,MACA,SACa;AACb,SAAO,qBAAqB,MAAM,SAAS,MAAS;AACrD;AAGO,SAAS,qBACf,MACA,SACA,QACa;AACb,SAAO,IAAI,WAAW,MAAM,SAAS,MAAM;AAC5C;AAEO,SAAS,aAAa,KAAiC;AAC7D,SAAO,GAAG,KAAK,UAAU;AAC1B;;;ACxCO,IAAM,eAAe,OAAO,IAAI,sBAAsB;;;ACC7D,IAAAC;AAoBkBA,OAAA;AADX,IAAM,yBAAN,MAAkF;EAQxF,YACW,MACA,QACT;AAEQ,kCAEN,CAAC;AANM,SAAA,OAAA;AACA,SAAA,SAAA;EACR;EAMH,KAAK,QAA8B;AAClC,SAAK,OAAO,OAAO;AACnB,WAAO;EACR;AACD;AApBC,cADY,wBACKA,MAAsB;AApBxC,IAAAA,MAAAC;AA0CO,IAAM,cAAN,eAAyDA,OAAA,wBACrCD,OAAA,YADqCC,MAAwC;EAGvG,GACC,IACuF;AACvF,QAAI,OAAO,OAAO,YAAY;AAC7B,WAAK,GAAG,IAAI,aAAa,CAAC;IAC3B;AACA,UAAM,iBAAiB,IAAI,sBAAuC;MACjE,OAAO,KAAK;MACZ,aAAa;MACb,oBAAoB;MACpB,qBAAqB;IACtB,CAAC;AACD,UAAM,mBAAmB,IAAI,MAAM,GAAG,kBAAkB,GAAG,cAAc;AACzE,WAAO,IAAI;MACV,IAAI,OAAO;QACV,UAAU,KAAK;QACf,QAAQ;UACP,MAAM,KAAK;UACX,QAAQ,KAAK;UACb,gBAAgB;UAChB,OAAO,GAAG,OAAO,EAAE,aAAa;QACjC;MACD,CAAC;MACD;IACD;EACD;AACD;AA5BC,cADY,aACcD,MAAsB;AA3CjD,IAAAA,MAAAC;AAyEO,IAAM,oBAAN,eAGGA,OAAA,wBACiBD,OAAA,YADjBC,MAA2D;EAKpE,YACC,MACA,SACA,QACC;AACD,UAAM,MAAM,MAAM;AAPX;AAQP,SAAK,UAAU,gBAAgB,QAAQ,MAAM,OAAO,CAAC;EACtD;EAEA,WAAkF;AACjF,WAAO,IAAI;MACV,IAAI,OAAO;QACV,UAAU;QACV,QAAQ;UACP,MAAM,KAAK;UACX,QAAQ,KAAK;UACb,gBAAgB,KAAK;UACrB,OAAO;QACR;MACD,CAAC;MACD,IAAI,sBAAsB;QACzB,OAAO,KAAK;QACZ,aAAa;QACb,oBAAoB;QACpB,qBAAqB;MACtB,CAAC;IACF;EACD;EAEA,GAAG,OAAoF;AACtF,WAAO,IAAI;MACV,IAAI,OAAO;QACV,UAAU,KAAK;QACf,QAAQ;UACP,MAAM,KAAK;UACX,QAAQ,KAAK;UACb,gBAAgB,KAAK;UACrB,OAAO,MAAM,aAAa;QAC3B;MACD,CAAC;MACD,IAAI,sBAAsB;QACzB,OAAO,KAAK;QACZ,aAAa;QACb,oBAAoB;QACpB,qBAAqB;MACtB,CAAC;IACF;EACD;AACD;AApDC,cAJY,mBAIcD,MAAsB;AA7EjD,IAAAA;AAyJkBA,OAAA;AADX,IAAM,8BAAN,MAAuF;EAQ7F,YACW,MACA,QACT;AAEQ,kCAKN,CAAC;AATM,SAAA,OAAA;AACA,SAAA,SAAA;EACR;EASH,MAAM,OAAqB;AAC1B,SAAK,OAAO,QAAQ;AACpB,WAAO;EACR;EAEA,KAAK,QAA4C;AAChD,SAAK,OAAO,OAAO;AACnB,WAAO;EACR;EAEA,WAAW,YAA0B;AACpC,SAAK,OAAO,aAAa;AACzB,WAAO;EACR;EAEA,aAAmB;AAClB,SAAK,OAAO,aAAa;AACzB,WAAO;EACR;AACD;AAtCC,cADY,6BACKA,MAAsB;AAzJxC,IAAAA,MAAAC;AAiMO,IAAM,0BAAN,eACEA,OAAA,6BAEkBD,OAAA,YAFlBC,MACT;EAGC,GACC,IACmG;AACnG,QAAI,OAAO,OAAO,YAAY;AAC7B,WAAK,GAAG,IAAI,aAAa,CAAC;IAC3B;AACA,UAAM,iBAAiB,IAAI,sBAAuC;MACjE,OAAO,KAAK;MACZ,aAAa;MACb,oBAAoB;MACpB,qBAAqB;IACtB,CAAC;AACD,UAAM,mBAAmB,IAAI,MAAM,GAAG,kBAAkB,GAAG,cAAc;AACzE,WAAO,IAAI;MACV,IAAI,mBAAmB;QACtB,UAAU;UACT,MAAM,KAAK,OAAO;UAClB,OAAO,KAAK,OAAO;UACnB,YAAY,KAAK,OAAO;UACxB,YAAY,KAAK,OAAO;QACzB;QACA,QAAQ;UACP,MAAM,KAAK;UACX,QAAQ,KAAK;UACb,gBAAgB;UAChB,OAAO,GAAG,OAAO,EAAE,aAAa;QACjC;MACD,CAAC;MACD;IACD;EACD;AACD;AAjCC,cAHY,yBAGcD,MAAsB;AApMjD,IAAAA,MAAAC;AAuOO,IAAM,gCAAN,eAGGA,OAAA,6BACiBD,OAAA,YADjBC,MAAgE;EAKzE,YACC,MACA,SACA,QACC;AACD,UAAM,MAAM,MAAM;AAPX;AAQP,SAAK,UAAU,gBAAgB,QAAQ,MAAM,OAAO,CAAC;EACtD;EAEA,WAA8F;AAC7F,WAAO,IAAI;MACV,IAAI,mBAAmB;QACtB,UAAU;UACT,YAAY,KAAK,OAAO;UACxB,OAAO,KAAK,OAAO;UACnB,MAAM,KAAK,OAAO;UAClB,YAAY,KAAK,OAAO;QACzB;QACA,QAAQ;UACP,MAAM,KAAK;UACX,QAAQ,KAAK;UACb,gBAAgB,KAAK;UACrB,OAAO;QACR;MACD,CAAC;MACD,IAAI,sBAAsB;QACzB,OAAO,KAAK;QACZ,aAAa;QACb,oBAAoB;QACpB,qBAAqB;MACtB,CAAC;IACF;EACD;EAEA,GAAG,OAAgG;AAClG,WAAO,IAAI;MACV,IAAI,mBAAmB;QACtB,UAAU;UACT,YAAY,KAAK,OAAO;UACxB,OAAO,KAAK,OAAO;UACnB,MAAM,KAAK,OAAO;UAClB,YAAY,KAAK,OAAO;QACzB;QACA,QAAQ;UACP,MAAM,KAAK;UACX,QAAQ,KAAK;UACb,gBAAgB,KAAK;UACrB,OAAO,MAAM,aAAa;QAC3B;MACD,CAAC;MACD,IAAI,sBAAsB;QACzB,OAAO,KAAK;QACZ,aAAa;QACb,oBAAoB;QACpB,qBAAqB;MACtB,CAAC;IACF;EACD;AACD;AA9DC,cAJY,+BAIcD,MAAsB;AA3OjD,IAAAA,MAAAC,MAAAC;AA2SO,IAAM,SAAN,eAIGA,MAAA,YACiBD,OAAA,YAEzBD,OAAA,cAHQE,KAA8C;EAOvD,YAAY,EAAE,UAAU,OAAO,GAU5B;AACF,UAAM,MAAM;AAfb,wBAACF;AAgBA,QAAI,UAAU;AACb,WAAK,YAAY,IAAI;QACpB,MAAM,SAAS;MAChB;IACD;EACD;AACD;AAxBC,cALY,QAKcC,MAAsB;AAgC1C,IAAM,2BAA2B,OAAO,IAAI,kCAAkC;AAhVrF,IAAAD,MAAAC,MAAAC;AAkVO,IAAM,qBAAN,eAIGA,MAAA,YACiBD,OAAA,YAEhBD,OAAA,0BAHDE,KAA8C;EAUvD,YAAY,EAAE,UAAU,OAAO,GAa5B;AACF,UAAM,MAAM;AArBb,wBAAUF;AAsBT,SAAK,wBAAwB,IAAI;MAChC,MAAM,qCAAU;MAChB,OAAO,qCAAU;MACjB,YAAY,qCAAU;MACtB,YAAY,qCAAU;IACvB;EACD;AACD;AA/BC,cALY,oBAKcC,MAAsB;AAwC1C,SAAS,iBACf,MACA,WACA,QACkC;AAClC,MAAI,WAAW;AACd,WAAO,IAAI,kBAAkB,MAAM,WAAW,MAAM;EACrD;AACA,SAAO,IAAI,YAAY,MAAM,MAAM;AACpC;AAGO,SAAS,6BACf,MACA,WACA,QAC0D;AAC1D,MAAI,WAAW;AACd,WAAO,IAAI,8BAA8B,MAAM,WAAW,MAAM;EACjE;AACA,SAAO,IAAI,wBAAwB,MAAM,MAAM;AAChD;AAOO,SAAS,OAAO,MAAc,SAAgF;AACpH,SAAO,iBAAiB,MAAM,SAAS,MAAS;AACjD;AAOO,SAAS,mBACf,MACA,SAC0D;AAC1D,SAAO,6BAA6B,MAAM,SAAS,MAAS;AAC7D;AAEO,SAAS,SAAS,KAA6B;AACrD,SAAO,GAAG,KAAK,MAAM;AACtB;AAEO,SAAS,qBAAqB,KAAyC;AAC7E,SAAO,GAAG,KAAK,kBAAkB;AAClC;;;AClbA,IAAAE;AASkBA,OAAA;AADX,IAAM,WAAN,MAAoE;EAE1E,YACiB,YACf;AAEF,iCAA2B,CAAC,MAAM,SAAS,gBAAgB;AAC1D,aAAO,kBAAkB,MAAM,SAAS,aAAa,KAAK,UAAU;IACrE;AAEA,gCAAQ,CAAC,MAAM,YAAY;AAC1B,aAAO,iBAAiB,MAAM,SAAS,KAAK,UAAU;IACvD;AAEA,4CAAoB,CAAC,MAAM,YAAY;AACtC,aAAO,6BAA6B,MAAM,SAAS,KAAK,UAAU;IACnE;AAEA,gCAAuB,CAAC,MAAM,WAAW;AACxC,aAAO,iBAAiB,MAAM,QAAQ,KAAK,UAAU;IACtD;AAEA,oCAA+B,CAAC,MAAM,YAAY;AACjD,aAAO,qBAAqB,MAAM,SAAS,KAAK,UAAU;IAC3D;AArBiB,SAAA,aAAA;EACd;EAsBH,SAAc;AACb,WAAO,IAAI,IAAI,CAAC,IAAI,WAAW,KAAK,UAAU,CAAC,CAAC;EACjD;EAEA,sBAA+B;AAC9B,WAAO;EACR;AACD;AAhCC,cADY,UACKA,MAAsB;AAkCjC,SAAS,WAAW,KAA+B;AACzD,SAAO,GAAG,KAAK,QAAQ;AACxB;AAEO,SAAS,SAA2B,MAAS;AACnD,MAAI,SAAS,UAAU;AACtB,UAAM,IAAI;MACT;IACD;EACD;AAEA,SAAO,IAAI,SAAS,IAAI;AACzB;;;ACvDA,IAAAC;AAoCkBA,OAAA;AAnBX,IAAe,kBAAf,MAAuF;EAC7F,YAAsB,OAAc;AAE1B;AAmBV;;AArBsB,SAAA,QAAA;EAAe;EAIrC,WAAkB;AACjB,WAAO,KAAK;EACb;EAEA,UAAU,UAAmB,cAAiC;AAC7D,WAAO;EACR;;EAGA,SAAS,OAAuB;AAC/B,SAAK,YAAY;AACjB,WAAO;EACR;AAkBD;AAhBC,cAnBqB,iBAmBJA,MAAsB;AApCxC,IAAAA;AAiEkBA,OAAA;AALX,IAAe,YAAf,MAIL;EAGD,YAAsB,SAAoB;AAApB,SAAA,UAAA;EAAqB;;EAc3C,QAAW,OAAY,OAAmC;AACzD,WAAO,OAAO,gBAAgB,qBAAqB,MAAM;AACxD,YAAM,WAAW,OAAO,gBAAgB,wBAAwB,MAAM;AACrE,eAAO,KAAK;UACX,KAAK,QAAQ,WAAW,KAAK;UAC7B;UACA;UACA;QACD;MACD,CAAC;AAED,aAAO,SAAS,SAAS,KAAK,EAAE,QAAQ,QAAW,KAAK;IACzD,CAAC;EACF;EAEA,IAAiB,OAA0B;AAC1C,WAAO,KAAK;MACX,KAAK,QAAQ,WAAW,KAAK;MAC7B;MACA;MACA;IACD,EAAE,IAAI;EACP;;EAMA,MAAM,MAAMC,MAAU,OAAwC;AAC7D,UAAM,MAAM,MAAM,KAAK,QAA6BA,MAAK,KAAK;AAE9D,WAAO;MACN,IAAI,CAAC,EAAE,OAAO;IACf;EACD;AAMD;AAxDC,cALqB,WAKJD,MAAsB;AAjExC,IAAAA,MAAAE;AA2HO,IAAe,gBAAf,eAIGA,OAAA,YACiBF,OAAA,YADjBE,MAA+C;EAGxD,YACC,SACA,SACU,QAKS,cAAc,GAChC;AACD,UAAM,SAAS,SAAS,MAAM;AAPpB,SAAA,SAAA;AAKS,SAAA,cAAA;EAGpB;EAEA,WAAkB;AACjB,UAAM,IAAI,yBAAyB;EACpC;;EAGA,wBAAwB,QAAkC;AACzD,UAAM,SAAmB,CAAC;AAC1B,QAAI,OAAO,gBAAgB;AAC1B,aAAO,KAAK,mBAAmB,OAAO,cAAc,EAAE;IACvD;AACA,QAAI,OAAO,YAAY;AACtB,aAAO,KAAK,OAAO,UAAU;IAC9B;AACA,QAAI,OAAO,OAAO,eAAe,WAAW;AAC3C,aAAO,KAAK,OAAO,aAAa,eAAe,gBAAgB;IAChE;AACA,WAAO,IAAI,IAAI,OAAO,KAAK,GAAG,CAAC;EAChC;EAEA,eAAe,QAA4C;AAC1D,WAAO,KAAK,QAAQ,QAAQ,sBAAsB,KAAK,wBAAwB,MAAM,CAAC,EAAE;EACzF;AAKD;AAzCC,cALqB,eAKKF,MAAsB;;;ACjH1C,SAAS,eAAuC,OAAe;AACrE,QAAM,UAAU,OAAO,OAAO,MAAM,MAAM,OAAO,OAAO,CAAC;AACzD,QAAM,UAAmB,CAAC;AAC1B,QAAM,SAAkB,CAAC;AACzB,QAAM,cAA4B,CAAC;AACnC,QAAM,cAA4B,OAAO,OAAO,MAAM,QAAQ,OAAO,iBAAiB,CAAC;AACvF,QAAM,oBAAwC,CAAC;AAC/C,QAAM,OAAO,MAAM,MAAM,OAAO,IAAI;AACpC,QAAM,SAAS,MAAM,MAAM,OAAO,MAAM;AACxC,QAAM,WAAuB,CAAC;AAC9B,QAAM,YAAqB,MAAM,QAAQ,OAAO,SAAS;AAEzD,QAAM,qBAAqB,MAAM,QAAQ,OAAO,kBAAkB;AAElE,MAAI,uBAAuB,QAAW;AACrC,UAAM,cAAc,mBAAmB,MAAM,MAAM,OAAO,kBAAkB,CAAC;AAC7E,UAAM,cAAc,MAAM,QAAQ,WAAW,IAAI,YAAY,KAAK,CAAC,IAAa,OAAO,OAAO,WAAW;AACzG,eAAW,WAAW,aAAa;AAClC,UAAI,GAAG,SAAS,YAAY,GAAG;AAC9B,gBAAQ,KAAK,QAAQ,MAAM,KAAK,CAAC;MAClC,WAAW,GAAG,SAAS,YAAY,GAAG;AACrC,eAAO,KAAK,QAAQ,MAAM,KAAK,CAAC;MACjC,WAAW,GAAG,SAAS,uBAAuB,GAAG;AAChD,0BAAkB,KAAK,QAAQ,MAAM,KAAK,CAAC;MAC5C,WAAW,GAAG,SAAS,iBAAiB,GAAG;AAC1C,oBAAY,KAAK,QAAQ,MAAM,KAAK,CAAC;MACtC,WAAW,GAAG,SAAS,iBAAiB,GAAG;AAC1C,oBAAY,KAAK,QAAQ,MAAM,KAAK,CAAC;MACtC,WAAW,GAAG,SAAS,QAAQ,GAAG;AACjC,iBAAS,KAAK,OAAO;MACtB;IACD;EACD;AAEA,SAAO;IACN;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACD;AACD;AAEO,SAAS,cAGd,MAAgC;AACjC,SAAO;IACN,GAAG,KAAK,cAAc;IACtB,GAAG,KAAK,YAAY;EACrB;AACD;AAEO,SAAS,0BAGd,MAA4C;AAC7C,SAAO;IACN,GAAG,KAAK,cAAc;IACtB,GAAG,KAAK,wBAAwB;EACjC;AACD;", "names": ["alias", "_a", "_a", "_a", "_a", "_a", "_b", "_a", "alias", "index", "table", "select", "sql", "joinOn", "field", "_a", "_a", "_b", "alias", "_a", "alias", "_a", "_b", "_a", "_b", "_a", "_b", "tableName", "_a", "_b", "_a", "_b", "_a", "_b", "sql", "_a", "alias", "_a", "unique", "_a", "_a", "_a", "_a", "_b", "_c", "_a", "_a", "sql", "_b"]}