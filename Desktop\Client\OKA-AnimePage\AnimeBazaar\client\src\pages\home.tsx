import { <PERSON> } from "wouter";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { useQuery } from "@tanstack/react-query";
import ItemCard from "@/components/item-card";
import SellerCard from "@/components/seller-card";
import { Badge } from "@/components/ui/badge";
import { Shield, Star, Users, ShoppingBag, Plus } from "lucide-react";

export default function Home() {
  const { data: featuredItems = [] } = useQuery({
    queryKey: ['/api/items/featured'],
  });

  const { data: categories = [] } = useQuery({
    queryKey: ['/api/categories'],
  });

  // Mock top sellers data for demonstration
  const topSellers = [
    {
      id: "1",
      username: "AnimeCollector123",
      firstName: "Anime",
      lastName: "Collector",
      email: "<EMAIL>",
      password: "",
      avatar: null,
      isSeller: true,
      rating: "4.9",
      totalReviews: 2450,
      totalSales: 2456,
      followers: 3200,
      bio: "Passionate anime figure collector",
      createdAt: new Date(),
      itemCount: 150,
    },
    {
      id: "2",
      username: "MangaMaster_JP",
      firstName: "Manga",
      lastName: "Master",
      email: "<EMAIL>",
      password: "",
      avatar: null,
      isSeller: true,
      rating: "4.8",
      totalReviews: 1892,
      totalSales: 1890,
      followers: 2800,
      bio: "Rare manga specialist",
      createdAt: new Date(),
      itemCount: 200,
    },
    {
      id: "3",
      username: "CosplayQueen",
      firstName: "Cosplay",
      lastName: "Queen",
      email: "<EMAIL>",
      password: "",
      avatar: null,
      isSeller: true,
      rating: "4.9",
      totalReviews: 1567,
      totalSales: 1245,
      followers: 4100,
      bio: "Professional cosplay costumes",
      createdAt: new Date(),
      itemCount: 89,
    },
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="gradient-anime-primary py-20 relative overflow-hidden anime-particles">
        <div className="container mx-auto px-4 relative z-10">
          <div className="text-center text-white">
            <h1 className="text-5xl md:text-7xl font-bold mb-6 anime-slide-up anime-glow text-white" style={{fontFamily: 'Orbitron, sans-serif'}}>
              Your Anime Universe Awaits!
            </h1>
            <p className="text-xl md:text-2xl font-light mb-8 opacity-90 anime-slide-up" style={{animationDelay: '0.3s'}}>
              Discover, trade, and collect amazing anime merchandise from fellow otaku
            </p>
            <div className="flex flex-col sm:flex-row gap-6 justify-center items-center anime-slide-up" style={{animationDelay: '0.6s'}}>
              <Link href="/marketplace">
                <Button className="anime-button px-10 py-4 text-lg rounded-full font-semibold impact-effect speed-lines">
                  <ShoppingBag className="mr-2 h-5 w-5" />
                  Start Shopping
                </Button>
              </Link>
              <Link href="/sell">
                <Button 
                  variant="outline"
                  className="px-10 py-4 text-lg border-2 border-white text-white rounded-full font-semibold hover:bg-white hover:text-primary transition-all cel-shaded anime-float"
                >
                  <Plus className="mr-2 h-5 w-5" />
                  Become a Seller
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Category Navigation */}
      <section className="bg-white py-8 shadow-sm">
        <div className="container mx-auto px-4">
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map((category: any) => (
              <Link key={category.id} href={`/marketplace?category=${category.id}`}>
                <div className="flex flex-col items-center p-4 rounded-xl hover:bg-gray-50 transition-colors cursor-pointer group anime-scale-in hover-lift">
                  <div className={`w-16 h-16 bg-gradient-to-r ${category.color} rounded-full flex items-center justify-center mb-2 group-hover:scale-110 transition-transform anime-bounce cel-shaded`}>
                    <span className="text-white text-2xl">
                      {category.icon?.includes('robot') ? '🤖' : 
                       category.icon?.includes('book') ? '📚' : 
                       category.icon?.includes('mask') ? '🎭' : 
                       category.icon?.includes('image') ? '🖼️' : 
                       category.icon?.includes('gem') ? '💎' : 
                       category.icon?.includes('card') ? '🃏' : '⭐'}
                    </span>
                  </div>
                  <span className="text-sm font-medium text-neutral-700">{category.name}</span>
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Items */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-800 mb-4 anime-slide-up" style={{fontFamily: 'Orbitron, sans-serif'}}>Featured Collections</h2>
            <p className="text-lg text-neutral-600 anime-slide-up" style={{animationDelay: '0.2s'}}>Handpicked items from trusted sellers in our community</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {featuredItems.slice(0, 4).map((item: any, index: number) => (
              <div key={item.id} className="anime-scale-in" style={{animationDelay: `${index * 0.1}s`}}>
                <ItemCard item={item} />
              </div>
            ))}
          </div>

          {featuredItems.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500">No featured items available at the moment.</p>
            </div>
          )}
        </div>
      </section>

      {/* Seller Spotlight */}
      <section className="bg-gradient-to-r from-gray-50 to-gray-100 py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-800 mb-4">Top Sellers This Month</h2>
            <p className="text-lg text-neutral-600">Meet our most trusted community members</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {topSellers.map((seller) => (
              <SellerCard key={seller.id} seller={seller} />
            ))}
          </div>
        </div>
      </section>

      {/* Trust & Safety */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-neutral-800 mb-4">Why Choose AnimeMarket?</h2>
            <p className="text-lg text-neutral-600">Your safety and satisfaction are our top priorities</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-20 h-20 gradient-anime-primary rounded-full flex items-center justify-center mx-auto mb-4">
                <Shield className="text-white text-3xl" />
              </div>
              <h3 className="text-xl font-semibold text-neutral-800 mb-2">Buyer Protection</h3>
              <p className="text-neutral-600">Get your money back if items don't match their description or never arrive</p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 gradient-anime-secondary rounded-full flex items-center justify-center mx-auto mb-4">
                <Star className="text-white text-3xl" />
              </div>
              <h3 className="text-xl font-semibold text-neutral-800 mb-2">Verified Reviews</h3>
              <p className="text-neutral-600">All reviews come from verified purchases to ensure authenticity</p>
            </div>

            <div className="text-center">
              <div className="w-20 h-20 gradient-anime-accent rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="text-white text-3xl" />
              </div>
              <h3 className="text-xl font-semibold text-neutral-800 mb-2">Community Driven</h3>
              <p className="text-neutral-600">Join a passionate community of anime fans who share your interests</p>
            </div>
          </div>
        </div>
      </section>

      {/* Newsletter Signup */}
      <section className="gradient-anime-primary py-16">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto text-center text-white">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">Stay in the Loop!</h2>
            <p className="text-xl mb-8 opacity-90">Get notified about new arrivals, exclusive deals, and community events</p>
            
            <div className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
              <Input 
                type="email" 
                placeholder="Enter your email" 
                className="flex-1 bg-white text-neutral-800"
              />
              <Button className="px-6 py-3 bg-white text-primary rounded-full font-semibold hover:shadow-lg transition-all transform hover:scale-105">
                Subscribe
              </Button>
            </div>
            
            <p className="text-sm opacity-75 mt-4">No spam, unsubscribe anytime</p>
          </div>
        </div>
      </section>
    </div>
  );
}
