{"hash": "885141b5", "configHash": "ba8b039c", "lockfileHash": "38c95428", "browserHash": "72caaf2b", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "6f7a11de", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "d443637c", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "d66f93e0", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "9f086540", "needsInterop": true}, "@hookform/resolvers/zod": {"src": "../../@hookform/resolvers/zod/dist/zod.mjs", "file": "@hookform_resolvers_zod.js", "fileHash": "6357d511", "needsInterop": false}, "@radix-ui/react-avatar": {"src": "../../@radix-ui/react-avatar/dist/index.mjs", "file": "@radix-ui_react-avatar.js", "fileHash": "ab6278a4", "needsInterop": false}, "@radix-ui/react-checkbox": {"src": "../../@radix-ui/react-checkbox/dist/index.mjs", "file": "@radix-ui_react-checkbox.js", "fileHash": "962840ec", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "733157fe", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "4ee4393c", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "2ef3e10c", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "5b7af712", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "f783d709", "needsInterop": false}, "@radix-ui/react-toast": {"src": "../../@radix-ui/react-toast/dist/index.mjs", "file": "@radix-ui_react-toast.js", "fileHash": "8038cd9d", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "9f788ed7", "needsInterop": false}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "27d96c6a", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "bf6f07ca", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "d75f477a", "needsInterop": false}, "drizzle-orm": {"src": "../../drizzle-orm/index.js", "file": "drizzle-orm.js", "fileHash": "5514e067", "needsInterop": false}, "drizzle-orm/pg-core": {"src": "../../drizzle-orm/pg-core/index.js", "file": "drizzle-orm_pg-core.js", "fileHash": "229be719", "needsInterop": false}, "drizzle-zod": {"src": "../../drizzle-zod/index.mjs", "file": "drizzle-zod.js", "fileHash": "b72e0945", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "d8fc202b", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "f20f80e8", "needsInterop": true}, "react-hook-form": {"src": "../../react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "9afa4619", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "ff282cfe", "needsInterop": false}, "wouter": {"src": "../../wouter/esm/index.js", "file": "wouter.js", "fileHash": "e017a612", "needsInterop": false}, "zod": {"src": "../../zod/lib/index.mjs", "file": "zod.js", "fileHash": "8efa9efa", "needsInterop": false}, "zustand": {"src": "../../zustand/esm/index.mjs", "file": "zustand.js", "fileHash": "6287d7da", "needsInterop": false}, "zustand/middleware": {"src": "../../zustand/esm/middleware.mjs", "file": "zustand_middleware.js", "fileHash": "c37dcb86", "needsInterop": false}}, "chunks": {"chunk-WGQNECLP": {"file": "chunk-WGQNECLP.js"}, "chunk-B62XNPVF": {"file": "chunk-B62XNPVF.js"}, "chunk-37IGQLUV": {"file": "chunk-37IGQLUV.js"}, "chunk-4RO2SXZU": {"file": "chunk-4RO2SXZU.js"}, "chunk-MABMAUKA": {"file": "chunk-MABMAUKA.js"}, "chunk-QVNMLYJ3": {"file": "chunk-QVNMLYJ3.js"}, "chunk-TUC2ZAOS": {"file": "chunk-TUC2ZAOS.js"}, "chunk-AM225P6L": {"file": "chunk-AM225P6L.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-4BFJKWC4": {"file": "chunk-4BFJKWC4.js"}, "chunk-43G6IMPT": {"file": "chunk-43G6IMPT.js"}, "chunk-35CYPHFM": {"file": "chunk-35CYPHFM.js"}, "chunk-IXXTE5L5": {"file": "chunk-IXXTE5L5.js"}, "chunk-4GTJGSR3": {"file": "chunk-4GTJGSR3.js"}, "chunk-GFOGKHJ2": {"file": "chunk-GFOGKHJ2.js"}, "chunk-BOL2CCOG": {"file": "chunk-BOL2CCOG.js"}, "chunk-EVLBUYTV": {"file": "chunk-EVLBUYTV.js"}, "chunk-RPCDYKBN": {"file": "chunk-RPCDYKBN.js"}, "chunk-KBTYAULA": {"file": "chunk-KBTYAULA.js"}, "chunk-QCHXOAYK": {"file": "chunk-QCHXOAYK.js"}, "chunk-WOOG5QLI": {"file": "chunk-WOOG5QLI.js"}}}